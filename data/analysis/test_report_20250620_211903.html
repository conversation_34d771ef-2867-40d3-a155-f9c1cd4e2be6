
<!DOCTYPE html>
<html>
<head>
    <title>sMTm Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .test-result {{ margin: 10px 0; padding: 10px; border-radius: 5px; }}
        .passed {{ background-color: #d4edda; }}
        .failed {{ background-color: #f8d7da; }}
        .details {{ margin-top: 10px; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>sMTm Mobile Test Report</h1>
        <p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>

    <div class="summary">
        <h2>Test Summary</h2>
        <p>Total Tests: {total_tests}</p>
        <p>Passed: {passed_tests}</p>
        <p>Failed: {failed_tests}</p>
        <p>Success Rate: {(passed_tests/total_tests*100):.1f}%</p>
    </div>

    <div class="results">
        <h2>Test Results</h2>

        <div class="test-result {status_class}">
            <h3>{result['scenario']} - {result['status']}</h3>
            <div class="details">
                <p>File: {result['test_file']}</p>
                <p>Timestamp: {result['timestamp']}</p>

            </div>
        </div>

    </div>
</body>
</html>
