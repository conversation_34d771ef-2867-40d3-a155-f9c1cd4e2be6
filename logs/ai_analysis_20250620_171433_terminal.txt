
================================================================================
sMTm Framework - Terminal Log Session
================================================================================
Session ID: ai_analysis_20250620_171433
Start Time: 2025-06-20 17:14:33
Command: /ai-analysis
================================================================================

[17:14:33] [INFO] 🚀 AI Analysis Started for app: Unknown
[17:14:33] [INFO] ============================================================
[17:14:33] [INFO] 📱 AI Analysis - session_start: AI Analysis session started: ai_analysis_20250620_171433
[17:14:33] [INFO] 📋 === AI ANALYSIS SESSION ===
[17:14:33] [INFO]   session_id: ai_analysis_20250620_171433
[17:14:33] [INFO]   start_time: 2025-06-20T17:14:33.386835
[17:14:33] [INFO]   custom_instructions: comprehensive
[17:14:33] [INFO]   analysis_mode: comprehensive_ui_analysis
[17:14:33] [INFO] 📋 === END AI ANALYSIS SESSION ===
[17:14:33] [STDOUT] Detected OS: macOS
[17:14:33] [RICH_CONSOLE] Detected OS: macOS
[17:14:33] [INFO] 📱 AI Analysis - os_detection: Detected OS: macOS
[17:14:33] [INFO] 📋 === OS DETECTION ===
[17:14:33] [INFO]   detected_os: macOS
[17:14:33] [INFO]   detection_method: OSDetector
[17:14:33] [INFO]   timestamp: 2025-06-20T17:14:33.387392
[17:14:33] [INFO] 📋 === END OS DETECTION ===
[17:14:33] [STDOUT] Use existing installation? (y/n):
[17:14:33] [RICH_CONSOLE] Use existing installation? (y/n):
[17:15:45] [INFO] 📱 AI Analysis - installation_preference: Use existing installation: True
[17:15:45] [STDOUT] What mobile OS would you like to analyze?
[17:15:45] [RICH_CONSOLE] What mobile OS would you like to analyze?
[17:15:45] [STDOUT] 1. Android
[17:15:45] [RICH_CONSOLE] 1. Android
[17:15:45] [STDOUT] 2. iOS
[17:15:45] [RICH_CONSOLE] 2. iOS
[17:15:45] [STDOUT] Enter your choice:
[17:15:45] [RICH_CONSOLE] Enter your choice:
[17:16:30] [INFO] 📱 AI Analysis - mobile_os_selection: Selected mobile OS: android
[17:16:30] [STDOUT] 
[17:16:30] [INFO] 📱 AI Analysis - android_setup_start: Starting Android environment setup
[17:16:30] [STDOUT] Checking Android environment...
⠋ Processing command...
[17:16:30] [RICH_CONSOLE] Checking Android environment...
[17:16:30] [STDOUT] 
Android emulator is already running
⠋ Processing command...
[17:16:30] [RICH_CONSOLE] Android emulator is already running
[17:16:30] [STDOUT] 
Connecting to Android device...
⠋ Processing command...
[17:16:30] [RICH_CONSOLE] Connecting to Android device...
[17:16:30] [STDOUT] Connecting to Android device...
[17:16:30] [STDOUT] 🔍 Validating device readiness: emulator-5554
[17:16:30] [STDOUT]   📡 Testing basic shell connectivity (attempt 1/3, timeout: 8s)...
[17:16:30] [STDOUT]   ✅ Shell connectivity OK (attempt 1)
[17:16:30] [STDOUT]   🚀 Checking boot completion status...
[17:16:30] [STATUS] Processing command...
[17:16:30] [STDOUT]   ✅ Device fully booted
[17:16:30] [STDOUT]   📱 Testing device responsiveness...
[17:16:30] [STDOUT]   ✅ Device responsive (Android 14)
[17:16:30] [STDOUT] 🎉 Device emulator-5554 is ready for connection!
[17:16:30] [STDOUT] ✅ 1 device(s) fully ready
[17:16:30] [STDOUT] 📱 Found 1 device(s)
[17:16:30] [STDOUT]   Device 1: emulator-5554 (status: device)
[17:16:30] [STDOUT] 🔗 Attempting to connect to device: emulator-5554
[17:16:30] [STDOUT] 🔧 Preparing device for connection...
[17:16:32] [STDOUT] 🔄 Trying direct connection strategy (timeout: 20s)...
[17:16:32] [STDOUT]   🔌 Attempting uiautomator2 connection...
[17:16:33] [STDOUT]   🧪 Verifying connection...
[17:16:33] [STDOUT] ✓ Device connection established using direct strategy
[17:16:33] [STDOUT]   📱 Device: sdk_gphone64_arm64
[17:16:33] [STDOUT] 
✓ Android Hardware Button Controller initialized
⠏ Processing command...
[17:16:33] [RICH_CONSOLE] ✓ Android Hardware Button Controller initialized
[17:16:33] [STDOUT] 
✓ Android environment setup completed!
⠏ Processing command...
[17:16:33] [RICH_CONSOLE] ✓ Android environment setup completed!
[17:16:33] [INFO] 📋 === ANDROID SETUP SUCCESS ===
[17:16:33] [INFO]   setup_duration: 3.13s
[17:16:33] [INFO]   device_connected: False
[17:16:33] [INFO]   emulator_status: unknown
[17:16:33] [INFO]   android_version: unknown
[17:16:33] [INFO] 📋 === END ANDROID SETUP SUCCESS ===
[17:16:33] [INFO] 📱 AI Analysis - android_setup_success: Android environment setup completed
[17:16:33] [INFO] 📱 AI Analysis - app_selection_start: Starting app selection and preparation
[17:16:33] [STDOUT] 📱 Analyzing available applications...
[17:16:33] [RICH_CONSOLE] 📱 Analyzing available applications...
[17:16:33] [STDOUT] 📋 Available applications:
[17:16:33] [RICH_CONSOLE] 📋 Available applications:
[17:16:33] [STDOUT]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[17:16:33] [RICH_CONSOLE]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[17:16:33] [STDOUT]   2. Rumah Pendidikan (APK File)
[17:16:33] [RICH_CONSOLE]   2. Rumah Pendidikan (APK File)
[17:16:33] [STDOUT] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[17:16:33] [RICH_CONSOLE] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[17:16:33] [STDOUT] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[17:16:33] [RICH_CONSOLE] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[17:16:33] [STDOUT] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[17:16:33] [RICH_CONSOLE] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[17:16:38] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[17:16:38] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[17:16:38] [STDOUT] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[17:16:38] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[17:16:38] [STDOUT] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[17:16:38] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[17:16:38] [STDOUT] 🚀 Skipping launch - proceeding directly to analysis
[17:16:38] [RICH_CONSOLE] 🚀 Skipping launch - proceeding directly to analysis
[17:16:38] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[17:16:38] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[17:16:38] [INFO] 📱 AI Analysis - app_selected: Selected app: com.kemendikdasmen.rumahpendidikan
[17:16:38] [INFO] 📱 AI Analysis - custom_instructions: Custom analysis: comprehensive
[17:16:38] [STDOUT] 🎯 Custom Analysis Instructions: comprehensive
[17:16:38] [RICH_CONSOLE] 🎯 Custom Analysis Instructions: comprehensive
[17:16:38] [INFO] 🚀 AI Analysis Started for app: com.kemendikdasmen.rumahpendidikan
[17:16:38] [INFO] ============================================================
[17:16:38] [STDOUT] 
[17:16:38] [INFO] 📱 AI Analysis - ui_analysis_start: Starting deep UI analysis
[17:16:38] [INFO] 📋 === UI ANALYSIS CONFIGURATION ===
[17:16:38] [INFO]   custom_instructions: comprehensive
[17:16:38] [INFO]   target_elements: 3000
[17:16:38] [INFO]   analysis_mode: comprehensive
[17:16:38] [INFO]   app_package: com.kemendikdasmen.rumahpendidikan
[17:16:38] [INFO] 📋 === END UI ANALYSIS CONFIGURATION ===
[17:16:38] [STDOUT] 🔍 Starting sequential, organized UI analysis...
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] 🔍 Starting sequential, organized UI analysis...
[17:16:38] [STDOUT] 
🔧 Initializing AI improvement system...
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] 🔧 Initializing AI improvement system...
[17:16:38] [STDOUT] 
✅ Professional Hybrid Headless Detector initialized
⠋ Processing command...
[17:16:38] [STDOUT] 
✅ Enhanced Element Classifier with Mobile Locator Knowledge initialized
⠋ Processing command...
[17:16:38] [STDOUT] 
✅ Real-time Problem Solver initialized
⠋ Processing command...
[17:16:38] [STDOUT] 
✅ AI Self-Improvement System initialized successfully
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] ✅ AI Self-Improvement System initialized successfully
[17:16:38] [STDOUT] 
🔧 AI improvement system initialization completed
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] 🔧 AI improvement system initialization completed
[17:16:38] [STDOUT] 
✅ AI improvement system initialized successfully
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] ✅ AI improvement system initialized successfully
[17:16:38] [STDOUT] 
🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] 🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
[17:16:38] [STDOUT] 
✅ collect_all_elements_robustly method exists on analyzer
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists on analyzer
[17:16:38] [STDOUT] 
🔍 Method type: <class 'method'>
⠋ Processing command...
[17:16:38] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[17:16:43] [STDOUT] 
📱 Current app: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[17:16:43] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[17:16:43] [STDOUT] 
🎯 Target app: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[17:16:43] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[17:16:43] [STDOUT] 
✅ Verified: Analysis starting in correct main app
⠸ Processing command...
[17:16:43] [RICH_CONSOLE] ✅ Verified: Analysis starting in correct main app
[17:16:43] [STDOUT] 
🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
⠸ Processing command...
[17:16:43] [RICH_CONSOLE] 🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
[17:16:43] [STDOUT] 
✅ No corrupted Excel files found
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] ✅ No corrupted Excel files found
[17:16:43] [INFO] 📋 === LOOP PREVENTION SYSTEM RESET ===
[17:16:43] [INFO]   emergency_exit_triggered: False
[17:16:43] [INFO]   analysis_start_time: 2025-06-20T17:16:43.670945
[17:16:43] [INFO]   timeout_minutes: 45
[17:16:43] [INFO]   consecutive_failures_threshold: 8
[17:16:43] [INFO]   reset_timestamp: 2025-06-20T17:16:43.670956
[17:16:43] [INFO] 📋 === END LOOP PREVENTION SYSTEM RESET ===
[17:16:43] [STDOUT] 
✅ Loop prevention system reset for new analysis session
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] ✅ Loop prevention system reset for new analysis session
[17:16:43] [STDOUT] 
🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
[17:16:43] [STDOUT] 
🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
[17:16:43] [STDOUT] 
🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
[17:16:43] [STDOUT] 
📋 AI will autonomously discover and analyze entire application hierarchy
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 📋 AI will autonomously discover and analyze entire application hierarchy
[17:16:43] [STDOUT] 
🎯 Target: Complete menu structure, sub-menus, and all navigation elements
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 🎯 Target: Complete menu structure, sub-menus, and all navigation elements
[17:16:43] [STDOUT] 
🚀 ENHANCED: 3000+ element collection with 60-minute duration
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 🚀 ENHANCED: 3000+ element collection with 60-minute duration
[17:16:43] [STDOUT] 
📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
[17:16:43] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE TARGET:
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE TARGET:
[17:16:43] [STDOUT] 
   • Beranda with all subsections (1.1-1.15)
⠼ Processing command...
[17:16:43] [RICH_CONSOLE]    • Beranda with all subsections (1.1-1.15)
[17:16:43] [STDOUT] 
   • Ruang GTK with all sub-elements (1.4.1-1.4.19)
⠼ Processing command...
[17:16:43] [RICH_CONSOLE]    • Ruang GTK with all sub-elements (1.4.1-1.4.19)
[17:16:43] [STDOUT] 
   • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
⠼ Processing command...
[17:16:43] [RICH_CONSOLE]    • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
[17:16:43] [STDOUT] 
   • Complete navigation bar elements
⠼ Processing command...
[17:16:43] [RICH_CONSOLE]    • Complete navigation bar elements
[17:16:43] [STDOUT] 
   • External app first-page elements only
⠼ Processing command...
[17:16:43] [RICH_CONSOLE]    • External app first-page elements only
[17:16:43] [STDOUT] 
⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
⠼ Processing command...
[17:16:43] [RICH_CONSOLE] ⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
[17:16:48] [STDOUT] 
🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[17:16:48] [RICH_CONSOLE] 🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
[17:16:48] [STDOUT] 
🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
⠧ Processing command...
[17:16:48] [RICH_CONSOLE] 🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
[17:16:48] [STDOUT] 
🎯 Main app context set: com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[17:16:48] [STDOUT] 
🧠 Intelligent navigation system initialized
⠧ Processing command...
[17:16:48] [RICH_CONSOLE] 🧠 Intelligent navigation system initialized
[17:16:48] [STDOUT] 
📂 Loading existing elements from persistent file: rumahpendidikan_persistent_analysis.xlsx
⠧ Processing command...
[17:16:48] [STDOUT] 
✅ Loaded 307 existing elements from persistent file
⠋ Processing command...
[17:16:48] [STDOUT] 
✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] ✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
[17:16:48] [STDOUT] 
✅ Persistent Excel file is valid: rumahpendidikan_persistent_analysis.xlsx
⠋ Processing command...
[17:16:48] [STDOUT] 
📋 Using persistent Excel storage only - no separate live save files created
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 📋 Using persistent Excel storage only - no separate live save files created
[17:16:48] [STDOUT] 
🎯 Performance Goals Tracking Started:
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 🎯 Performance Goals Tracking Started:
[17:16:48] [STDOUT] 
  • Target Elements: 3000
⠋ Processing command...
[17:16:48] [RICH_CONSOLE]   • Target Elements: 3000
[17:16:48] [STDOUT] 
  • Target Duration: 60 minutes
⠋ Processing command...
[17:16:48] [RICH_CONSOLE]   • Target Duration: 60 minutes
[17:16:48] [STDOUT] 
  • Max Unknown Elements: 3.0%
⠋ Processing command...
[17:16:48] [RICH_CONSOLE]   • Max Unknown Elements: 3.0%
[17:16:48] [STDOUT] 
🔍 Real-time problem monitoring started in background
⠋ Processing command...
[17:16:48] [STDOUT] 
🔧 Real-time problem solver monitoring started
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 🔧 Real-time problem solver monitoring started
[17:16:48] [STDOUT] 
🔍 Searching for previous analysis state for rumahpendidikan...
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 🔍 Searching for previous analysis state for rumahpendidikan...
[17:16:48] [STDOUT] 
📋 Checking persistent Excel manager for analysis state...
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 📋 Checking persistent Excel manager for analysis state...
[17:16:48] [STDOUT] 
⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] ⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
[17:16:48] [STDOUT] 
📂 Falling back to search for legacy live analysis files...
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 📂 Falling back to search for legacy live analysis files...
[17:16:48] [STDOUT] 
📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
[17:16:48] [STDOUT] 
⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] ⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
[17:16:48] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
[17:16:48] [STDOUT] 
📋 Target: Complete Rumah Pendidikan UI hierarchy collection
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 📋 Target: Complete Rumah Pendidikan UI hierarchy collection
[17:16:48] [STDOUT] 
📋 Phase 1: Comprehensive main page analysis and feature identification...
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 📋 Phase 1: Comprehensive main page analysis and feature identification...
[17:16:48] [STDOUT] 
🔍 Collecting all elements from main page...
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 🔍 Collecting all elements from main page...
[17:16:48] [STDOUT] 
✅ collect_all_elements_robustly method exists
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists
[17:16:48] [STDOUT] 
🔍 Method type: <class 'method'>
⠋ Processing command...
[17:16:48] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[17:16:49] [STDOUT] 🛡️ ROBUST ELEMENT COLLECTION STARTED
[17:16:49] [STDOUT] 📋 Strategy: Checkpoint-based progressive collection with crash recovery
[17:16:49] [STDOUT] 📋 Initializing robust collection state...
[17:16:49] [STDOUT] 📊 Found 307 existing elements in persistent storage
[17:16:49] [STDOUT] ✅ Collection state initialized
[17:16:49] [STDOUT] 🎯 Starting progressive collection with checkpoints...
[17:16:49] [STDOUT] 📋 SECTION 1/9: Beranda
[17:16:54] [STDOUT] 📍 Checkpoint created for section: Beranda
[17:16:54] [STDOUT] 🔍 Collecting elements from section: Beranda
[17:16:54] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:16:54] [STDOUT] 📸 Using screenshot-based collection strategy
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ⏭️ EXISTING element: 
[17:16:54] [STDOUT] ✅ Selector '//*' found 17 elements
[17:16:54] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:16:54] [STDOUT] ✅ Section 'Beranda' completed: 17 elements
[17:16:54] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠋ Processing command...
[17:16:54] [STDOUT] 
📊 Elements in memory: 307
⠋ Processing command...
[17:16:54] [STDOUT] 
📊 Elements list prepared: 307
⠋ Processing command...
[17:16:54] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠋ Processing command...
[17:16:54] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠋ Processing command...
[17:16:54] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠋ Processing command...
[17:16:54] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠹ Processing command...
[17:16:54] [STDOUT] 
📊 Creating Statistics sheet...
⠹ Processing command...
[17:16:54] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠹ Processing command...
[17:16:54] [STDOUT] 
📊 Creating Metadata sheet...
⠹ Processing command...
[17:16:54] [STDOUT] 
✅ Metadata sheet created
⠹ Processing command...
[17:16:54] [STDOUT] 
✅ Persistent Excel file saved successfully
⠸ Processing command...
[17:16:54] [STDOUT] 
📊 Total elements in memory: 307
⠸ Processing command...
[17:16:54] [STDOUT] 
📊 Total elements saved: 307
⠸ Processing command...
[17:16:54] [STDOUT] 
🆕 New elements: 0
⠸ Processing command...
[17:16:54] [STDOUT] 
🔄 Updated elements: 0
⠸ Processing command...
[17:16:54] [STDOUT] 
⏭️ Skipped elements: 17
⠸ Processing command...
[17:16:54] [STDOUT] 💾 Progress saved: 17 elements, 1 sections
[17:16:54] [STDOUT] 📋 SECTION 2/9: Ruang GTK
[17:16:59] [STDOUT] 📍 Checkpoint created for section: Ruang GTK
[17:16:59] [STDOUT] 🔍 Collecting elements from section: Ruang GTK
[17:16:59] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:16:59] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ⏭️ EXISTING element: 
[17:17:00] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:00] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:00] [STDOUT] ✅ Section 'Ruang GTK' completed: 17 elements
[17:17:00] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠇ Processing command...
[17:17:00] [STDOUT] 
📊 Elements in memory: 307
⠇ Processing command...
[17:17:00] [STDOUT] 
📊 Elements list prepared: 307
⠇ Processing command...
[17:17:00] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠇ Processing command...
[17:17:00] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠏ Processing command...
[17:17:00] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠏ Processing command...
[17:17:00] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠏ Processing command...
[17:17:00] [STDOUT] 
📊 Creating Statistics sheet...
⠏ Processing command...
[17:17:00] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠏ Processing command...
[17:17:00] [STDOUT] 
📊 Creating Metadata sheet...
⠏ Processing command...
[17:17:00] [STDOUT] 
✅ Metadata sheet created
⠏ Processing command...
[17:17:00] [STDOUT] 
✅ Persistent Excel file saved successfully
⠋ Processing command...
[17:17:00] [STDOUT] 
📊 Total elements in memory: 307
⠋ Processing command...
[17:17:00] [STDOUT] 
📊 Total elements saved: 307
⠋ Processing command...
[17:17:00] [STDOUT] 
🆕 New elements: 0
⠋ Processing command...
[17:17:00] [STDOUT] 
🔄 Updated elements: 0
⠋ Processing command...
[17:17:00] [STDOUT] 
⏭️ Skipped elements: 34
⠋ Processing command...
[17:17:00] [STDOUT] 💾 Progress saved: 34 elements, 2 sections
[17:17:00] [STDOUT] 📋 SECTION 3/9: Ruang Murid
[17:17:05] [STDOUT] 📍 Checkpoint created for section: Ruang Murid
[17:17:05] [STDOUT] 🔍 Collecting elements from section: Ruang Murid
[17:17:05] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:05] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ⏭️ EXISTING element: 
[17:17:05] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:05] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:05] [STDOUT] ✅ Section 'Ruang Murid' completed: 17 elements
[17:17:05] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠦ Processing command...
[17:17:05] [STDOUT] 
📊 Elements in memory: 307
⠦ Processing command...
[17:17:05] [STDOUT] 
📊 Elements list prepared: 307
⠦ Processing command...
[17:17:05] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠦ Processing command...
[17:17:05] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠦ Processing command...
[17:17:05] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠦ Processing command...
[17:17:05] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠧ Processing command...
[17:17:05] [STDOUT] 
📊 Creating Statistics sheet...
⠧ Processing command...
[17:17:05] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠧ Processing command...
[17:17:05] [STDOUT] 
📊 Creating Metadata sheet...
⠧ Processing command...
[17:17:05] [STDOUT] 
✅ Metadata sheet created
⠧ Processing command...
[17:17:05] [STDOUT] 
✅ Persistent Excel file saved successfully
⠇ Processing command...
[17:17:05] [STDOUT] 
📊 Total elements in memory: 307
⠇ Processing command...
[17:17:05] [STDOUT] 
📊 Total elements saved: 307
⠇ Processing command...
[17:17:05] [STDOUT] 
🆕 New elements: 0
⠇ Processing command...
[17:17:05] [STDOUT] 
🔄 Updated elements: 0
⠇ Processing command...
[17:17:05] [STDOUT] 
⏭️ Skipped elements: 51
⠇ Processing command...
[17:17:05] [STDOUT] 💾 Progress saved: 51 elements, 3 sections
[17:17:05] [STDOUT] 📋 SECTION 4/9: Ruang sekolah
[17:17:10] [STDOUT] 📍 Checkpoint created for section: Ruang sekolah
[17:17:10] [STDOUT] 🔍 Collecting elements from section: Ruang sekolah
[17:17:10] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:10] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ⏭️ EXISTING element: 
[17:17:10] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:10] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:10] [STDOUT] ✅ Section 'Ruang sekolah' completed: 17 elements
[17:17:10] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠸ Processing command...
[17:17:10] [STDOUT] 
📊 Elements in memory: 307
⠸ Processing command...
[17:17:10] [STDOUT] 
📊 Elements list prepared: 307
⠸ Processing command...
[17:17:10] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠸ Processing command...
[17:17:10] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠸ Processing command...
[17:17:10] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠼ Processing command...
[17:17:10] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠼ Processing command...
[17:17:10] [STDOUT] 
📊 Creating Statistics sheet...
⠼ Processing command...
[17:17:10] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠼ Processing command...
[17:17:10] [STDOUT] 
📊 Creating Metadata sheet...
⠼ Processing command...
[17:17:10] [STDOUT] 
✅ Metadata sheet created
⠼ Processing command...
[17:17:11] [STDOUT] 
✅ Persistent Excel file saved successfully
⠴ Processing command...
[17:17:11] [STDOUT] 
📊 Total elements in memory: 307
⠴ Processing command...
[17:17:11] [STDOUT] 
📊 Total elements saved: 307
⠴ Processing command...
[17:17:11] [STDOUT] 
🆕 New elements: 0
⠴ Processing command...
[17:17:11] [STDOUT] 
🔄 Updated elements: 0
⠴ Processing command...
[17:17:11] [STDOUT] 
⏭️ Skipped elements: 68
⠴ Processing command...
[17:17:11] [STDOUT] 💾 Progress saved: 68 elements, 4 sections
[17:17:11] [STDOUT] 📋 SECTION 5/9: Ruang bahasa
[17:17:16] [STDOUT] 📍 Checkpoint created for section: Ruang bahasa
[17:17:16] [STDOUT] 🔍 Collecting elements from section: Ruang bahasa
[17:17:16] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:16] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ⏭️ EXISTING element: 
[17:17:16] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:16] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:16] [STDOUT] ✅ Section 'Ruang bahasa' completed: 17 elements
[17:17:16] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠙ Processing command...
[17:17:16] [STDOUT] 
📊 Elements in memory: 307
⠙ Processing command...
[17:17:16] [STDOUT] 
📊 Elements list prepared: 307
⠙ Processing command...
[17:17:16] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠙ Processing command...
[17:17:16] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠙ Processing command...
[17:17:16] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠙ Processing command...
[17:17:16] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠹ Processing command...
[17:17:16] [STDOUT] 
📊 Creating Statistics sheet...
⠹ Processing command...
[17:17:16] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠹ Processing command...
[17:17:16] [STDOUT] 
📊 Creating Metadata sheet...
⠹ Processing command...
[17:17:16] [STDOUT] 
✅ Metadata sheet created
⠹ Processing command...
[17:17:16] [STDOUT] 
✅ Persistent Excel file saved successfully
⠸ Processing command...
[17:17:16] [STDOUT] 
📊 Total elements in memory: 307
⠸ Processing command...
[17:17:16] [STDOUT] 
📊 Total elements saved: 307
⠸ Processing command...
[17:17:16] [STDOUT] 
🆕 New elements: 0
⠸ Processing command...
[17:17:16] [STDOUT] 
🔄 Updated elements: 0
⠸ Processing command...
[17:17:16] [STDOUT] 
⏭️ Skipped elements: 85
⠸ Processing command...
[17:17:16] [STDOUT] 💾 Progress saved: 85 elements, 5 sections
[17:17:16] [STDOUT] 📋 SECTION 6/9: Ruang pemerintah
[17:17:21] [STDOUT] 📍 Checkpoint created for section: Ruang pemerintah
[17:17:21] [STDOUT] 🔍 Collecting elements from section: Ruang pemerintah
[17:17:21] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:21] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ⏭️ EXISTING element: 
[17:17:21] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:21] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:21] [STDOUT] ✅ Section 'Ruang pemerintah' completed: 17 elements
[17:17:21] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠇ Processing command...
[17:17:21] [STDOUT] 
📊 Elements in memory: 307
⠇ Processing command...
[17:17:21] [STDOUT] 
📊 Elements list prepared: 307
⠇ Processing command...
[17:17:21] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠇ Processing command...
[17:17:21] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠇ Processing command...
[17:17:21] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠇ Processing command...
[17:17:21] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠋ Processing command...
[17:17:21] [STDOUT] 
📊 Creating Statistics sheet...
⠋ Processing command...
[17:17:21] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠋ Processing command...
[17:17:21] [STDOUT] 
📊 Creating Metadata sheet...
⠋ Processing command...
[17:17:21] [STDOUT] 
✅ Metadata sheet created
⠋ Processing command...
[17:17:21] [STDOUT] 
✅ Persistent Excel file saved successfully
⠙ Processing command...
[17:17:21] [STDOUT] 
📊 Total elements in memory: 307
⠙ Processing command...
[17:17:21] [STDOUT] 
📊 Total elements saved: 307
⠙ Processing command...
[17:17:21] [STDOUT] 
🆕 New elements: 0
⠙ Processing command...
[17:17:21] [STDOUT] 
🔄 Updated elements: 0
⠙ Processing command...
[17:17:21] [STDOUT] 
⏭️ Skipped elements: 102
⠙ Processing command...
[17:17:21] [STDOUT] 💾 Progress saved: 102 elements, 6 sections
[17:17:21] [STDOUT] 📋 SECTION 7/9: Ruang mitra
[17:17:26] [STDOUT] 📍 Checkpoint created for section: Ruang mitra
[17:17:26] [STDOUT] 🔍 Collecting elements from section: Ruang mitra
[17:17:26] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:26] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ⏭️ EXISTING element: 
[17:17:27] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:27] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:27] [STDOUT] ✅ Section 'Ruang mitra' completed: 17 elements
[17:17:27] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠦ Processing command...
[17:17:27] [STDOUT] 
📊 Elements in memory: 307
⠦ Processing command...
[17:17:27] [STDOUT] 
📊 Elements list prepared: 307
⠦ Processing command...
[17:17:27] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠦ Processing command...
[17:17:27] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠦ Processing command...
[17:17:27] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠦ Processing command...
[17:17:27] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠧ Processing command...
[17:17:27] [STDOUT] 
📊 Creating Statistics sheet...
⠧ Processing command...
[17:17:27] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠧ Processing command...
[17:17:27] [STDOUT] 
📊 Creating Metadata sheet...
⠧ Processing command...
[17:17:27] [STDOUT] 
✅ Metadata sheet created
⠧ Processing command...
[17:17:27] [STDOUT] 
✅ Persistent Excel file saved successfully
⠇ Processing command...
[17:17:27] [STDOUT] 
📊 Total elements in memory: 307
⠇ Processing command...
[17:17:27] [STDOUT] 
📊 Total elements saved: 307
⠇ Processing command...
[17:17:27] [STDOUT] 
🆕 New elements: 0
⠇ Processing command...
[17:17:27] [STDOUT] 
🔄 Updated elements: 0
⠇ Processing command...
[17:17:27] [STDOUT] 
⏭️ Skipped elements: 119
⠇ Processing command...
[17:17:27] [STDOUT] 💾 Progress saved: 119 elements, 7 sections
[17:17:27] [STDOUT] 📋 SECTION 8/9: Ruang Publik
[17:17:32] [STDOUT] 📍 Checkpoint created for section: Ruang Publik
[17:17:32] [STDOUT] 🔍 Collecting elements from section: Ruang Publik
[17:17:32] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:32] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ⏭️ EXISTING element: 
[17:17:32] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:32] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:32] [STDOUT] ✅ Section 'Ruang Publik' completed: 17 elements
[17:17:32] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠸ Processing command...
[17:17:32] [STDOUT] 
📊 Elements in memory: 307
⠸ Processing command...
[17:17:32] [STDOUT] 
📊 Elements list prepared: 307
⠸ Processing command...
[17:17:32] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠸ Processing command...
[17:17:32] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠸ Processing command...
[17:17:32] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠸ Processing command...
[17:17:32] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠼ Processing command...
[17:17:32] [STDOUT] 
📊 Creating Statistics sheet...
⠼ Processing command...
[17:17:32] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠼ Processing command...
[17:17:32] [STDOUT] 
📊 Creating Metadata sheet...
⠼ Processing command...
[17:17:32] [STDOUT] 
✅ Metadata sheet created
⠼ Processing command...
[17:17:32] [STDOUT] 
✅ Persistent Excel file saved successfully
⠴ Processing command...
[17:17:32] [STDOUT] 
📊 Total elements in memory: 307
⠴ Processing command...
[17:17:32] [STDOUT] 
📊 Total elements saved: 307
⠴ Processing command...
[17:17:32] [STDOUT] 
🆕 New elements: 0
⠴ Processing command...
[17:17:32] [STDOUT] 
🔄 Updated elements: 0
⠴ Processing command...
[17:17:32] [STDOUT] 
⏭️ Skipped elements: 136
⠴ Processing command...
[17:17:32] [STDOUT] 💾 Progress saved: 136 elements, 8 sections
[17:17:32] [STDOUT] 📋 SECTION 9/9: Ruang Orang Tua
[17:17:37] [STDOUT] 📍 Checkpoint created for section: Ruang Orang Tua
[17:17:37] [STDOUT] 🔍 Collecting elements from section: Ruang Orang Tua
[17:17:37] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:37] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ⏭️ EXISTING element: 
[17:17:37] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:37] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:37] [STDOUT] ✅ Section 'Ruang Orang Tua' completed: 17 elements
[17:17:37] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠋ Processing command...
[17:17:37] [STDOUT] 
📊 Elements in memory: 307
⠋ Processing command...
[17:17:37] [STDOUT] 
📊 Elements list prepared: 307
⠋ Processing command...
[17:17:37] [STDOUT] 
📊 DataFrame created with 307 rows and 61 columns
⠋ Processing command...
[17:17:37] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠋ Processing command...
[17:17:37] [STDOUT] 
✅ Overview sheet created with 55 metrics
⠋ Processing command...
[17:17:37] [STDOUT] 
✅ Elements sheet saved with 307 rows
⠹ Processing command...
[17:17:37] [STDOUT] 
📊 Creating Statistics sheet...
⠹ Processing command...
[17:17:37] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠹ Processing command...
[17:17:37] [STDOUT] 
📊 Creating Metadata sheet...
⠹ Processing command...
[17:17:37] [STDOUT] 
✅ Metadata sheet created
⠹ Processing command...
[17:17:38] [STDOUT] 
✅ Persistent Excel file saved successfully
⠸ Processing command...
[17:17:38] [STDOUT] 
📊 Total elements in memory: 307
⠸ Processing command...
[17:17:38] [STDOUT] 
📊 Total elements saved: 307
⠸ Processing command...
[17:17:38] [STDOUT] 
🆕 New elements: 0
⠸ Processing command...
[17:17:38] [STDOUT] 
🔄 Updated elements: 0
⠸ Processing command...
[17:17:38] [STDOUT] 
⏭️ Skipped elements: 153
⠸ Processing command...
[17:17:38] [STDOUT] 💾 Progress saved: 153 elements, 9 sections
[17:17:38] [STDOUT] 🧭 Collecting navigation elements...
[17:17:38] [STDOUT] 🔍 Collecting elements from section: Navigation
[17:17:38] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[17:17:38] [STDOUT] 📸 Using screenshot-based collection strategy
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ⏭️ EXISTING element: 
[17:17:38] [STDOUT] ✅ Selector '//*' found 17 elements
[17:17:38] [STDOUT] ✅ Strategy successful: 17 elements collected
[17:17:38] [STDOUT] ✅ Navigation collection: 17 elements
[17:17:38] [STDOUT] 🔍 Verifying collection completeness...
[17:17:38] [STDOUT] ✅ Collection verification complete: 100.0% sections, Quality: 9.0/10
[17:17:38] [STDOUT] ✅ ROBUST COLLECTION COMPLETED: 170 elements
[17:17:38] [INFO] ⏱️ === PERFORMANCE METRICS ===
[17:17:38] [INFO]   total_elements: 307
[17:17:38] [INFO] ⏱️ === END PERFORMANCE METRICS ===
[17:17:38] [INFO] ⏱️ === PERFORMANCE METRICS ===
[17:17:38] [INFO]   analyzed_elements: 307
[17:17:38] [INFO] ⏱️ === END PERFORMANCE METRICS ===
[17:17:38] [INFO] 📱 [17:17:38.086] Device Action - element_collection: Successfully collected 307 elements
[17:17:38] [STDOUT] 
✅ Found 307 elements on main page
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] ✅ Found 307 elements on main page
[17:17:38] [STDOUT] 
✅ Identified 5 main features for comprehensive analysis
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] ✅ Identified 5 main features for comprehensive analysis
[17:17:38] [STDOUT] 
  Feature 1: Unknown (unknown)
⠼ Processing command...
[17:17:38] [RICH_CONSOLE]   Feature 1: Unknown (unknown)
[17:17:38] [STDOUT] 
  Feature 2: Unknown (unknown)
⠼ Processing command...
[17:17:38] [RICH_CONSOLE]   Feature 2: Unknown (unknown)
[17:17:38] [STDOUT] 
  Feature 3: Unknown (unknown)
⠼ Processing command...
[17:17:38] [RICH_CONSOLE]   Feature 3: Unknown (unknown)
[17:17:38] [STDOUT] 
  Feature 4: Unknown (unknown)
⠼ Processing command...
[17:17:38] [RICH_CONSOLE]   Feature 4: Unknown (unknown)
[17:17:38] [STDOUT] 
  Feature 5: Unknown (unknown)
⠼ Processing command...
[17:17:38] [RICH_CONSOLE]   Feature 5: Unknown (unknown)
[17:17:38] [STDOUT] 
📋 Phase 2: Comprehensive feature analysis (5 features)...
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 
📋 Phase 2: Comprehensive feature analysis (5 features)...
[17:17:38] [STDOUT] 
🎯 COMPREHENSIVE: AI will analyze each feature exhaustively for 3000+ elements
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 🎯 COMPREHENSIVE: AI will analyze each feature exhaustively for 3000+ elements
[17:17:38] [STDOUT] 
🎯 === ANALYZING FEATURE 1/5: 'Feature_0' (unknown) ===
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 1/5: 'Feature_0' (unknown) ===
[17:17:38] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Feature_0' for maximum element discovery
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Feature_0' for maximum element discovery
[17:17:38] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[17:17:38] [STDOUT] 
🏥 Pre-feature health check for 'Feature_0'...
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Feature_0'...
[17:17:38] [STDOUT] 
🏥 Checking application health...
⠼ Processing command...
[17:17:38] [RICH_CONSOLE] 🏥 Checking application health...
[17:17:43] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:17:43] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:17:43] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:17:43] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:17:43] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:17:43] [STDOUT] 
🚀 Found 17 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:17:43] [STDOUT] 
🚀 Found 51 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:17:43] [STDOUT] 
🚀 Found 51 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:17:43] [STDOUT] 
🚀 Found 85 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:17:43] [STDOUT] 
🚀 Found 98 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[17:17:43] [STDOUT] 
🚀 Found 132 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:17:43] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠏ Processing command...
[17:17:43] [STDOUT] 
✓ Classified 15 elements instantly
⠏ Processing command...
[17:17:43] [STDOUT] 
✓ Headless discovery found 15 elements
⠏ Processing command...
[17:17:43] [STDOUT] 
✅ Headless discovery: 15 elements
⠏ Processing command...
[17:17:43] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:17:43] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:17:43] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠏ Processing command...
[17:17:43] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠏ Processing command...
[17:17:43] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠏ Processing command...
[17:17:43] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠏ Processing command...
[17:17:43] [STDOUT] 
🎯 Performing targeted visual scan...
⠏ Processing command...
[17:17:43] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:17:43] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:17:43] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.46s
⠼ Processing command...
[17:17:43] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠼ Processing command...
[17:17:43] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[17:17:48] [STDOUT] 
✅ UI elements accessible: 15 elements found
⠧ Processing command...
[17:17:48] [RICH_CONSOLE] ✅ UI elements accessible: 15 elements found
[17:17:53] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:17:53] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:17:53] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:17:53] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:17:53] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:17:53] [STDOUT] 
🚀 Found 17 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:17:53] [STDOUT] 
🚀 Found 51 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:17:53] [STDOUT] 
🚀 Found 51 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:17:53] [STDOUT] 
🚀 Found 85 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:17:53] [STDOUT] 
🚀 Found 98 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:17:53] [STDOUT] 
🚀 Found 132 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:17:53] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠙ Processing command...
[17:17:53] [STDOUT] 
✓ Classified 15 elements instantly
⠙ Processing command...
[17:17:53] [STDOUT] 
✓ Headless discovery found 15 elements
⠙ Processing command...
[17:17:53] [STDOUT] 
✅ Headless discovery: 15 elements
⠙ Processing command...
[17:17:53] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:17:53] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:17:53] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠙ Processing command...
[17:17:53] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠙ Processing command...
[17:17:53] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[17:17:53] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[17:17:53] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[17:17:54] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:17:54] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:17:54] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.34s
⠼ Processing command...
[17:17:54] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:17:54] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:17:54] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:17:54] [STDOUT] 
✅ App is responsive
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] ✅ App is responsive
[17:17:54] [STDOUT] 
📊 Feature Feature_0 started:
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 📊 Feature Feature_0 started:
[17:17:54] [STDOUT] 
   Elements: 0
⠴ Processing command...
[17:17:54] [RICH_CONSOLE]    Elements: 0
[17:17:54] [STDOUT] 
   Interactions: 0
⠴ Processing command...
[17:17:54] [RICH_CONSOLE]    Interactions: 0
[17:17:54] [STDOUT] 
   Sub-features: 0
⠴ Processing command...
[17:17:54] [RICH_CONSOLE]    Sub-features: 0
[17:17:54] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 1.2min
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 1.2min
[17:17:54] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Feature_0'
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Feature_0'
[17:17:54] [STDOUT] 
❌ FAILED: Feature 'Feature_0': 'name'
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] ❌ FAILED: Feature 'Feature_0': 'name'
[17:17:54] [STDOUT] 
📊 Feature Feature_0 failed:
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 📊 Feature Feature_0 failed:
[17:17:54] [STDOUT] 
   Elements: 0
⠴ Processing command...
[17:17:54] [RICH_CONSOLE]    Elements: 0
[17:17:54] [STDOUT] 
   Interactions: 0
⠴ Processing command...
[17:17:54] [RICH_CONSOLE]    Interactions: 0
[17:17:54] [STDOUT] 
   Sub-features: 0
⠴ Processing command...
[17:17:54] [RICH_CONSOLE]    Sub-features: 0
[17:17:54] [STDOUT] 
🏥 Post-feature health check for 'Feature_0'...
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🏥 Post-feature health check for 'Feature_0'...
[17:17:54] [STDOUT] 
🏥 Checking application health...
⠴ Processing command...
[17:17:54] [RICH_CONSOLE] 🏥 Checking application health...
[17:17:59] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:17:59] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:17:59] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:17:59] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[17:17:59] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[17:17:59] [STDOUT] 
🚀 Found 17 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:17:59] [STDOUT] 
🚀 Found 51 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:17:59] [STDOUT] 
🚀 Found 51 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:17:59] [STDOUT] 
🚀 Found 85 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:17:59] [STDOUT] 
🚀 Found 98 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:17:59] [STDOUT] 
🚀 Found 132 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:17:59] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠋ Processing command...
[17:17:59] [STDOUT] 
✓ Classified 15 elements instantly
⠋ Processing command...
[17:17:59] [STDOUT] 
✓ Headless discovery found 15 elements
⠋ Processing command...
[17:17:59] [STDOUT] 
✅ Headless discovery: 15 elements
⠋ Processing command...
[17:17:59] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:17:59] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:17:59] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠋ Processing command...
[17:17:59] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠋ Processing command...
[17:17:59] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:17:59] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:17:59] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:17:59] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:17:59] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:17:59] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.37s
⠼ Processing command...
[17:17:59] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠼ Processing command...
[17:17:59] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[17:18:04] [STDOUT] 
✅ UI elements accessible: 15 elements found
⠧ Processing command...
[17:18:04] [RICH_CONSOLE] ✅ UI elements accessible: 15 elements found
[17:18:09] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:18:09] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:09] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:18:09] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:18:09] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:18:09] [STDOUT] 
🚀 Found 17 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:09] [STDOUT] 
🚀 Found 51 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:09] [STDOUT] 
🚀 Found 51 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:09] [STDOUT] 
🚀 Found 85 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:09] [STDOUT] 
🚀 Found 98 elements using widgets strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:09] [STDOUT] 
🚀 Found 132 elements using fallback strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:09] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠹ Processing command...
[17:18:09] [STDOUT] 
✓ Classified 15 elements instantly
⠹ Processing command...
[17:18:09] [STDOUT] 
✓ Headless discovery found 15 elements
⠹ Processing command...
[17:18:09] [STDOUT] 
✅ Headless discovery: 15 elements
⠹ Processing command...
[17:18:09] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠹ Processing command...
[17:18:09] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠹ Processing command...
[17:18:09] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠹ Processing command...
[17:18:09] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠹ Processing command...
[17:18:09] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[17:18:09] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[17:18:09] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[17:18:10] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:18:10] [STDOUT] 
✅ Visual validation: additional elements found
⠴ Processing command...
[17:18:10] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.34s
⠴ Processing command...
[17:18:10] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:10] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:18:10] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:10] [STDOUT] 
✅ App is responsive
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] ✅ App is responsive
[17:18:10] [STDOUT] 
🔄 CRITICAL: Ensuring return to main page before next feature...
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🔄 CRITICAL: Ensuring return to main page before next feature...
[17:18:10] [STDOUT] 
🔙 Ensuring return to main page...
⠴ Processing command...
[17:18:10] [RICH_CONSOLE] 🔙 Ensuring return to main page...
[17:18:15] [STDOUT] 
✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
⠇ Processing command...
[17:18:15] [RICH_CONSOLE] ✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
[17:18:15] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:18:15] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:15] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:18:15] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:18:15] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:18:15] [STDOUT] 
🚀 Found 17 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:15] [STDOUT] 
🚀 Found 51 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:15] [STDOUT] 
🚀 Found 51 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:15] [STDOUT] 
🚀 Found 85 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:15] [STDOUT] 
🚀 Found 98 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:15] [STDOUT] 
🚀 Found 132 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:15] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠋ Processing command...
[17:18:15] [STDOUT] 
✓ Classified 15 elements instantly
⠋ Processing command...
[17:18:15] [STDOUT] 
✓ Headless discovery found 15 elements
⠋ Processing command...
[17:18:15] [STDOUT] 
✅ Headless discovery: 15 elements
⠋ Processing command...
[17:18:15] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:18:15] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:18:15] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠋ Processing command...
[17:18:15] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠋ Processing command...
[17:18:15] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:18:15] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:18:15] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:18:15] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠸ Processing command...
[17:18:15] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:18:15] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.40s
⠼ Processing command...
[17:18:15] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:15] [STDOUT] 
🔙 Back navigation attempt 1/5 (in main app)
⠼ Processing command...
[17:18:15] [RICH_CONSOLE] 🔙 Back navigation attempt 1/5 (in main app)
[17:18:17] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
⠸ Processing command...
[17:18:17] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
[17:18:17] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[17:18:17] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[17:18:17] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:18:17] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[17:18:17] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:18:17] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[17:18:17] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠼ Processing command...
[17:18:17] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[17:18:17] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:18:17] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[17:18:19] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:18:19] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[17:18:22] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[17:18:22] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[17:18:24] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[17:18:24] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:24] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[17:18:24] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[17:18:24] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[17:18:24] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:18:24] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:18:24] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:18:24] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:18:24] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[17:18:24] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[17:18:24] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠧ Processing command...
[17:18:24] [STDOUT] 
✓ Classified 21 elements instantly
⠧ Processing command...
[17:18:24] [STDOUT] 
✓ Headless discovery found 21 elements
⠧ Processing command...
[17:18:24] [STDOUT] 
✅ Headless discovery: 21 elements
⠧ Processing command...
[17:18:24] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[17:18:24] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:18:24] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠧ Processing command...
[17:18:24] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠧ Processing command...
[17:18:24] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[17:18:24] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[17:18:24] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[17:18:25] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠙ Processing command...
[17:18:25] [STDOUT] 
✅ Visual validation: additional elements found
⠙ Processing command...
[17:18:25] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.45s
⠙ Processing command...
[17:18:25] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠙ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:18:25] [STDOUT] 
✅ Main app restarted and ready with 21 elements
⠙ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Main app restarted and ready with 21 elements
[17:18:25] [STDOUT] 
✅ Main app restarted successfully
⠙ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Main app restarted successfully
[17:18:25] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[17:18:25] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:25] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[17:18:25] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠹ Processing command...
[17:18:25] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠹ Processing command...
[17:18:25] [STDOUT] 
✓ Classified 21 elements instantly
⠹ Processing command...
[17:18:25] [STDOUT] 
✓ Headless discovery found 21 elements
⠹ Processing command...
[17:18:25] [STDOUT] 
✅ Headless discovery: 21 elements
⠹ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠹ Processing command...
[17:18:25] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠹ Processing command...
[17:18:25] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠹ Processing command...
[17:18:25] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠹ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[17:18:25] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[17:18:25] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[17:18:25] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:18:25] [STDOUT] 
✅ Visual validation: additional elements found
⠴ Processing command...
[17:18:25] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.27s
⠴ Processing command...
[17:18:25] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:25] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:18:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:25] [STDOUT] 
✅ Main page verified - found indicator: ruang
⠦ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Main page verified - found indicator: ruang
[17:18:25] [STDOUT] 
✅ VERIFIED: On main page, ready for next feature
⠦ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ VERIFIED: On main page, ready for next feature
[17:18:25] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠦ Processing command...
[17:18:25] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:18:25] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:18:25] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:25] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:18:25] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[17:18:25] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠦ Processing command...
[17:18:25] [STDOUT] 
✓ Classified 21 elements instantly
⠦ Processing command...
[17:18:25] [STDOUT] 
✓ Headless discovery found 21 elements
⠦ Processing command...
[17:18:25] [STDOUT] 
✅ Headless discovery: 21 elements
⠦ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[17:18:25] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[17:18:25] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠦ Processing command...
[17:18:25] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠦ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[17:18:25] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[17:18:25] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[17:18:25] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:18:25] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:18:25] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.24s
⠏ Processing command...
[17:18:25] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:18:25] [STDOUT] 
⚠️ No interactive elements found, but device appears responsive
⠏ Processing command...
[17:18:25] [RICH_CONSOLE] ⚠️ No interactive elements found, but device appears responsive
[17:18:25] [STDOUT] 
✅ Terminal-emulator sync verified, ready for next feature
⠏ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ Terminal-emulator sync verified, ready for next feature
[17:18:25] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠏ Processing command...
[17:18:25] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:18:25] [STDOUT] 
🔍 Verifying terminal-emulator state consistency...
⠏ Processing command...
[17:18:25] [RICH_CONSOLE] 🔍 Verifying terminal-emulator state consistency...
[17:18:25] [STDOUT] 
✅ App package consistent: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:18:25] [RICH_CONSOLE] ✅ App package consistent: com.kemendikdasmen.rumahpendidikan
[17:18:25] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:18:25] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:25] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:18:25] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:25] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:18:25] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠋ Processing command...
[17:18:25] [STDOUT] 
✓ Classified 21 elements instantly
⠋ Processing command...
[17:18:25] [STDOUT] 
✓ Headless discovery found 21 elements
⠋ Processing command...
[17:18:25] [STDOUT] 
✅ Headless discovery: 21 elements
⠋ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:18:25] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:18:25] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:18:25] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:18:25] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:18:25] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:18:25] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:18:26] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:18:26] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:18:26] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.36s
⠼ Processing command...
[17:18:26] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠼ Processing command...
[17:18:26] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:18:26] [STDOUT] 
✅ UI elements accessible: 21 found
⠼ Processing command...
[17:18:26] [RICH_CONSOLE] ✅ UI elements accessible: 21 found
[17:18:31] [STDOUT] 
❌ App info timeout after 5s
⠧ Processing command...
[17:18:31] [RICH_CONSOLE] ❌ App info timeout after 5s
[17:18:31] [STDOUT] 
❌ App not responsive during consistency check
⠧ Processing command...
[17:18:31] [RICH_CONSOLE] ❌ App not responsive during consistency check
[17:18:31] [STDOUT] 
⚠️ Terminal-emulator sync issues detected
⠧ Processing command...
[17:18:31] [RICH_CONSOLE] ⚠️ Terminal-emulator sync issues detected
[17:18:31] [STDOUT] 
⏳ Brief pause for sync stabilization...
⠧ Processing command...
[17:18:31] [RICH_CONSOLE] ⏳ Brief pause for sync stabilization...
[17:18:34] [STDOUT] 
🎯 === ANALYZING FEATURE 2/5: 'Feature_1' (unknown) ===
⠼ Processing command...
[17:18:34] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 2/5: 'Feature_1' (unknown) ===
[17:18:34] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Feature_1' for maximum element discovery
⠼ Processing command...
[17:18:34] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Feature_1' for maximum element discovery
[17:18:34] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠼ Processing command...
[17:18:34] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[17:18:34] [STDOUT] 
🏥 Pre-feature health check for 'Feature_1'...
⠼ Processing command...
[17:18:34] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Feature_1'...
[17:18:34] [STDOUT] 
🏥 Checking application health...
⠼ Processing command...
[17:18:34] [RICH_CONSOLE] 🏥 Checking application health...
[17:18:39] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:18:39] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:39] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:18:39] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[17:18:39] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[17:18:39] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:39] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:39] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:39] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:39] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:39] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:39] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠏ Processing command...
[17:18:39] [STDOUT] 
✓ Classified 21 elements instantly
⠏ Processing command...
[17:18:39] [STDOUT] 
✓ Headless discovery found 21 elements
⠏ Processing command...
[17:18:39] [STDOUT] 
✅ Headless discovery: 21 elements
⠏ Processing command...
[17:18:39] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:18:39] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:18:39] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:18:39] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:18:39] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:18:39] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:18:39] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:18:39] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠸ Processing command...
[17:18:39] [STDOUT] 
✅ Visual validation: additional elements found
⠸ Processing command...
[17:18:39] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.32s
⠸ Processing command...
[17:18:39] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠸ Processing command...
[17:18:39] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:18:44] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠦ Processing command...
[17:18:44] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:18:49] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:18:49] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:49] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:18:49] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[17:18:49] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[17:18:49] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:49] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:49] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:49] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:49] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:49] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:18:49] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:18:49] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:18:49] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:18:49] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:18:49] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:18:49] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:18:49] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠙ Processing command...
[17:18:49] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠙ Processing command...
[17:18:49] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[17:18:49] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[17:18:49] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[17:18:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:18:50] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:18:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.34s
⠼ Processing command...
[17:18:50] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:18:50] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:18:50] [STDOUT] 
✅ App is responsive
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] ✅ App is responsive
[17:18:50] [STDOUT] 
📊 Feature Feature_1 started:
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 📊 Feature Feature_1 started:
[17:18:50] [STDOUT] 
   Elements: 0
⠼ Processing command...
[17:18:50] [RICH_CONSOLE]    Elements: 0
[17:18:50] [STDOUT] 
   Interactions: 0
⠼ Processing command...
[17:18:50] [RICH_CONSOLE]    Interactions: 0
[17:18:50] [STDOUT] 
   Sub-features: 0
⠼ Processing command...
[17:18:50] [RICH_CONSOLE]    Sub-features: 0
[17:18:50] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 2.1min
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 2.1min
[17:18:50] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Feature_1'
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Feature_1'
[17:18:50] [STDOUT] 
❌ FAILED: Feature 'Feature_1': 'name'
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] ❌ FAILED: Feature 'Feature_1': 'name'
[17:18:50] [STDOUT] 
📊 Feature Feature_1 failed:
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 📊 Feature Feature_1 failed:
[17:18:50] [STDOUT] 
   Elements: 0
⠼ Processing command...
[17:18:50] [RICH_CONSOLE]    Elements: 0
[17:18:50] [STDOUT] 
   Interactions: 0
⠼ Processing command...
[17:18:50] [RICH_CONSOLE]    Interactions: 0
[17:18:50] [STDOUT] 
   Sub-features: 0
⠼ Processing command...
[17:18:50] [RICH_CONSOLE]    Sub-features: 0
[17:18:50] [STDOUT] 
🏥 Post-feature health check for 'Feature_1'...
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🏥 Post-feature health check for 'Feature_1'...
[17:18:50] [STDOUT] 
🏥 Checking application health...
⠼ Processing command...
[17:18:50] [RICH_CONSOLE] 🏥 Checking application health...
[17:18:55] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:18:55] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:18:55] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:18:55] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:18:55] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:18:55] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:55] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:55] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:55] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:55] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:55] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:18:55] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠏ Processing command...
[17:18:55] [STDOUT] 
✓ Classified 21 elements instantly
⠏ Processing command...
[17:18:55] [STDOUT] 
✓ Headless discovery found 21 elements
⠏ Processing command...
[17:18:55] [STDOUT] 
✅ Headless discovery: 21 elements
⠏ Processing command...
[17:18:55] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:18:55] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:18:55] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠏ Processing command...
[17:18:55] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠏ Processing command...
[17:18:55] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠏ Processing command...
[17:18:55] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠏ Processing command...
[17:18:55] [STDOUT] 
🎯 Performing targeted visual scan...
⠏ Processing command...
[17:18:55] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:18:55] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:18:55] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.32s
⠹ Processing command...
[17:18:55] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:18:55] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:00] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠦ Processing command...
[17:19:00] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:19:05] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:19:05] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:19:05] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:19:05] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[17:19:05] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[17:19:05] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:19:05] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:19:05] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:19:05] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:19:05] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:19:05] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:19:05] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠋ Processing command...
[17:19:05] [STDOUT] 
✓ Classified 21 elements instantly
⠋ Processing command...
[17:19:05] [STDOUT] 
✓ Headless discovery found 21 elements
⠋ Processing command...
[17:19:05] [STDOUT] 
✅ Headless discovery: 21 elements
⠋ Processing command...
[17:19:05] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:19:05] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:19:05] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:19:05] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:19:05] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:19:05] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:19:05] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:19:05] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠸ Processing command...
[17:19:05] [STDOUT] 
✅ Visual validation: additional elements found
⠸ Processing command...
[17:19:05] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.30s
⠸ Processing command...
[17:19:05] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠸ Processing command...
[17:19:05] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:06] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:19:06] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:19:06] [STDOUT] 
✅ App is responsive
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] ✅ App is responsive
[17:19:06] [STDOUT] 
🔄 CRITICAL: Ensuring return to main page before next feature...
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🔄 CRITICAL: Ensuring return to main page before next feature...
[17:19:06] [STDOUT] 
🔙 Ensuring return to main page...
⠸ Processing command...
[17:19:06] [RICH_CONSOLE] 🔙 Ensuring return to main page...
[17:19:11] [STDOUT] 
✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
⠦ Processing command...
[17:19:11] [RICH_CONSOLE] ✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
[17:19:11] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:19:11] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:19:11] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:19:11] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:19:11] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:19:11] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:11] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:11] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:11] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:11] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:11] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:11] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠧ Processing command...
[17:19:11] [STDOUT] 
✓ Classified 21 elements instantly
⠧ Processing command...
[17:19:11] [STDOUT] 
✓ Headless discovery found 21 elements
⠧ Processing command...
[17:19:11] [STDOUT] 
✅ Headless discovery: 21 elements
⠧ Processing command...
[17:19:11] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[17:19:11] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:19:11] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠧ Processing command...
[17:19:11] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠧ Processing command...
[17:19:11] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[17:19:11] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[17:19:11] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[17:19:11] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:19:11] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:19:11] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.47s
⠹ Processing command...
[17:19:11] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:11] [STDOUT] 
🔙 Back navigation attempt 1/5 (in main app)
⠹ Processing command...
[17:19:11] [RICH_CONSOLE] 🔙 Back navigation attempt 1/5 (in main app)
[17:19:18] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
⠼ Processing command...
[17:19:18] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
[17:19:18] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:19:18] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[17:19:23] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[17:19:23] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[17:19:23] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[17:19:23] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[17:19:28] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠋ Processing command...
[17:19:28] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[17:19:28] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:19:28] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[17:19:30] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[17:19:30] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[17:19:33] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[17:19:33] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[17:19:40] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠼ Processing command...
[17:19:40] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:19:40] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠼ Processing command...
[17:19:40] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠼ Processing command...
[17:19:40] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠼ Processing command...
[17:19:40] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[17:19:40] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[17:19:40] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[17:19:40] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[17:19:40] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠴ Processing command...
[17:19:40] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠴ Processing command...
[17:19:40] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠴ Processing command...
[17:19:40] [STDOUT] 
✓ Classified 21 elements instantly
⠴ Processing command...
[17:19:40] [STDOUT] 
✓ Headless discovery found 21 elements
⠴ Processing command...
[17:19:40] [STDOUT] 
✅ Headless discovery: 21 elements
⠴ Processing command...
[17:19:40] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠴ Processing command...
[17:19:40] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠴ Processing command...
[17:19:40] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:19:40] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:19:40] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:19:40] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:19:40] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:19:40] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:19:40] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:19:40] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.44s
⠏ Processing command...
[17:19:40] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:19:40] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:40] [STDOUT] 
✅ Main app restarted and ready with 21 elements
⠏ Processing command...
[17:19:40] [RICH_CONSOLE] ✅ Main app restarted and ready with 21 elements
[17:19:40] [STDOUT] 
✅ Main app restarted successfully
⠏ Processing command...
[17:19:40] [RICH_CONSOLE] ✅ Main app restarted successfully
[17:19:46] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:19:46] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:19:46] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:19:46] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:19:46] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:19:46] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:46] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:46] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:46] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:46] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:46] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:46] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠦ Processing command...
[17:19:46] [STDOUT] 
✓ Classified 21 elements instantly
⠦ Processing command...
[17:19:46] [STDOUT] 
✓ Headless discovery found 21 elements
⠦ Processing command...
[17:19:46] [STDOUT] 
✅ Headless discovery: 21 elements
⠦ Processing command...
[17:19:46] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[17:19:46] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[17:19:46] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠦ Processing command...
[17:19:46] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠦ Processing command...
[17:19:46] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[17:19:46] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[17:19:46] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[17:19:46] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:19:46] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:19:46] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.67s
⠹ Processing command...
[17:19:46] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:19:46] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:19:46] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:19:46] [STDOUT] 
✅ Main page verified - found indicator: ruang
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] ✅ Main page verified - found indicator: ruang
[17:19:46] [STDOUT] 
✅ VERIFIED: On main page, ready for next feature
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] ✅ VERIFIED: On main page, ready for next feature
[17:19:46] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠹ Processing command...
[17:19:46] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:19:51] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[17:19:51] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:19:51] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[17:19:51] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[17:19:51] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[17:19:51] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:51] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:51] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:51] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:51] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:51] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[17:19:51] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠦ Processing command...
[17:19:51] [STDOUT] 
✓ Classified 21 elements instantly
⠦ Processing command...
[17:19:51] [STDOUT] 
✓ Headless discovery found 21 elements
⠦ Processing command...
[17:19:51] [STDOUT] 
✅ Headless discovery: 21 elements
⠦ Processing command...
[17:19:51] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[17:19:51] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:19:52] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠇ Processing command...
[17:19:52] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠇ Processing command...
[17:19:52] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[17:19:52] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[17:19:52] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[17:19:52] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠸ Processing command...
[17:19:52] [STDOUT] 
✅ Visual validation: additional elements found
⠸ Processing command...
[17:19:52] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.66s
⠸ Processing command...
[17:19:52] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠸ Processing command...
[17:19:52] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:52] [STDOUT] 
⚠️ No interactive elements found, but device appears responsive
⠸ Processing command...
[17:19:52] [RICH_CONSOLE] ⚠️ No interactive elements found, but device appears responsive
[17:19:52] [STDOUT] 
✅ Terminal-emulator sync verified, ready for next feature
⠸ Processing command...
[17:19:52] [RICH_CONSOLE] ✅ Terminal-emulator sync verified, ready for next feature
[17:19:52] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠸ Processing command...
[17:19:52] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:19:52] [STDOUT] 
🔍 Verifying terminal-emulator state consistency...
⠸ Processing command...
[17:19:52] [RICH_CONSOLE] 🔍 Verifying terminal-emulator state consistency...
[17:19:57] [STDOUT] 
✅ App package consistent: com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[17:19:57] [RICH_CONSOLE] ✅ App package consistent: com.kemendikdasmen.rumahpendidikan
[17:19:57] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠧ Processing command...
[17:19:57] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:19:57] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠧ Processing command...
[17:19:57] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠧ Processing command...
[17:19:57] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠧ Processing command...
[17:19:57] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:57] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:57] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:57] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:19:57] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[17:19:57] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠇ Processing command...
[17:19:57] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠇ Processing command...
[17:19:57] [STDOUT] 
✓ Classified 21 elements instantly
⠇ Processing command...
[17:19:57] [STDOUT] 
✓ Headless discovery found 21 elements
⠇ Processing command...
[17:19:57] [STDOUT] 
✅ Headless discovery: 21 elements
⠇ Processing command...
[17:19:57] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠇ Processing command...
[17:19:57] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠇ Processing command...
[17:19:57] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠇ Processing command...
[17:19:57] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠇ Processing command...
[17:19:57] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[17:19:57] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[17:19:57] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[17:19:57] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:19:57] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:19:57] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.39s
⠹ Processing command...
[17:19:57] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:19:57] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:19:57] [STDOUT] 
✅ UI elements accessible: 21 found
⠹ Processing command...
[17:19:57] [RICH_CONSOLE] ✅ UI elements accessible: 21 found
[17:20:02] [STDOUT] 
❌ App info timeout after 5s
⠼ Processing command...
[17:20:02] [RICH_CONSOLE] ❌ App info timeout after 5s
[17:20:02] [STDOUT] 
❌ App not responsive during consistency check
⠼ Processing command...
[17:20:02] [RICH_CONSOLE] ❌ App not responsive during consistency check
[17:20:02] [STDOUT] 
⚠️ Terminal-emulator sync issues detected
⠼ Processing command...
[17:20:02] [RICH_CONSOLE] ⚠️ Terminal-emulator sync issues detected
[17:20:02] [STDOUT] 
⏳ Brief pause for sync stabilization...
⠼ Processing command...
[17:20:02] [RICH_CONSOLE] ⏳ Brief pause for sync stabilization...
[17:20:05] [STDOUT] 
🎯 === ANALYZING FEATURE 3/5: 'Feature_2' (unknown) ===
⠹ Processing command...
[17:20:05] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 3/5: 'Feature_2' (unknown) ===
[17:20:05] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Feature_2' for maximum element discovery
⠹ Processing command...
[17:20:05] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Feature_2' for maximum element discovery
[17:20:05] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠹ Processing command...
[17:20:05] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[17:20:05] [STDOUT] 
🏥 Pre-feature health check for 'Feature_2'...
⠹ Processing command...
[17:20:05] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Feature_2'...
[17:20:05] [STDOUT] 
🏥 Checking application health...
⠹ Processing command...
[17:20:05] [RICH_CONSOLE] 🏥 Checking application health...
[17:20:11] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:20:11] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:20:11] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:20:11] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:20:11] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:20:11] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:20:11] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:20:11] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:20:11] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:20:11] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[17:20:11] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[17:20:11] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠧ Processing command...
[17:20:11] [STDOUT] 
✓ Classified 21 elements instantly
⠧ Processing command...
[17:20:11] [STDOUT] 
✓ Headless discovery found 21 elements
⠧ Processing command...
[17:20:11] [STDOUT] 
✅ Headless discovery: 21 elements
⠧ Processing command...
[17:20:11] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[17:20:11] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:20:11] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠇ Processing command...
[17:20:11] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠇ Processing command...
[17:20:11] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[17:20:11] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[17:20:11] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[17:20:11] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[17:20:11] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[17:20:11] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.33s
⠋ Processing command...
[17:20:11] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠙ Processing command...
[17:20:11] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:20:16] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠼ Processing command...
[17:20:16] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:20:21] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠧ Processing command...
[17:20:21] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:20:21] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠧ Processing command...
[17:20:21] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠧ Processing command...
[17:20:21] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠧ Processing command...
[17:20:21] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:20:21] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:20:21] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:20:21] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:20:21] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[17:20:21] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:20:21] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠏ Processing command...
[17:20:21] [STDOUT] 
✓ Classified 21 elements instantly
⠏ Processing command...
[17:20:21] [STDOUT] 
✓ Headless discovery found 21 elements
⠏ Processing command...
[17:20:21] [STDOUT] 
✅ Headless discovery: 21 elements
⠏ Processing command...
[17:20:21] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:20:21] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:20:21] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:20:21] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:20:21] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:20:21] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:20:21] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:20:22] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:20:22] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:20:22] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.61s
⠼ Processing command...
[17:20:22] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:22] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:20:22] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:20:22] [STDOUT] 
✅ App is responsive
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] ✅ App is responsive
[17:20:22] [STDOUT] 
📊 Feature Feature_2 started:
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 📊 Feature Feature_2 started:
[17:20:22] [STDOUT] 
   Elements: 0
⠼ Processing command...
[17:20:22] [RICH_CONSOLE]    Elements: 0
[17:20:22] [STDOUT] 
   Interactions: 0
⠼ Processing command...
[17:20:22] [RICH_CONSOLE]    Interactions: 0
[17:20:22] [STDOUT] 
   Sub-features: 0
⠼ Processing command...
[17:20:22] [RICH_CONSOLE]    Sub-features: 0
[17:20:22] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 3.6min
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 3.6min
[17:20:22] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Feature_2'
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Feature_2'
[17:20:22] [STDOUT] 
❌ FAILED: Feature 'Feature_2': 'name'
⠼ Processing command...
[17:20:22] [RICH_CONSOLE] ❌ FAILED: Feature 'Feature_2': 'name'
[17:20:22] [STDOUT] 
📊 Feature Feature_2 failed:
⠴ Processing command...
[17:20:22] [RICH_CONSOLE] 📊 Feature Feature_2 failed:
[17:20:22] [STDOUT] 
   Elements: 0
⠴ Processing command...
[17:20:22] [RICH_CONSOLE]    Elements: 0
[17:20:22] [STDOUT] 
   Interactions: 0
⠴ Processing command...
[17:20:22] [RICH_CONSOLE]    Interactions: 0
[17:20:22] [STDOUT] 
   Sub-features: 0
⠴ Processing command...
[17:20:22] [RICH_CONSOLE]    Sub-features: 0
[17:20:22] [STDOUT] 
🏥 Post-feature health check for 'Feature_2'...
⠴ Processing command...
[17:20:22] [RICH_CONSOLE] 🏥 Post-feature health check for 'Feature_2'...
[17:20:22] [STDOUT] 
🏥 Checking application health...
⠴ Processing command...
[17:20:22] [RICH_CONSOLE] 🏥 Checking application health...
[17:20:27] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:20:27] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:20:27] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:20:27] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[17:20:27] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[17:20:27] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:20:27] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:20:27] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:20:27] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:20:27] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:20:27] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:20:27] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠋ Processing command...
[17:20:27] [STDOUT] 
✓ Classified 21 elements instantly
⠋ Processing command...
[17:20:27] [STDOUT] 
✓ Headless discovery found 21 elements
⠋ Processing command...
[17:20:27] [STDOUT] 
✅ Headless discovery: 21 elements
⠋ Processing command...
[17:20:27] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:20:27] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:20:27] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:20:27] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:20:27] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:20:27] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:20:27] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:20:27] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[17:20:27] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:20:27] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.57s
⠦ Processing command...
[17:20:27] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:20:27] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:20:32] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠏ Processing command...
[17:20:32] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:20:38] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:20:38] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:20:38] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:20:38] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:20:38] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:20:38] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:20:38] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:20:38] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:20:38] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:38] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:38] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:38] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:20:38] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:20:38] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:20:38] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:20:38] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:20:38] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:20:38] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:20:38] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:20:38] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:20:38] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:20:38] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:20:38] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:20:38] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:20:38] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.54s
⠏ Processing command...
[17:20:38] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:20:38] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:20:38] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:20:38] [STDOUT] 
✅ App is responsive
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] ✅ App is responsive
[17:20:38] [STDOUT] 
🔄 CRITICAL: Ensuring return to main page before next feature...
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🔄 CRITICAL: Ensuring return to main page before next feature...
[17:20:38] [STDOUT] 
🔙 Ensuring return to main page...
⠋ Processing command...
[17:20:38] [RICH_CONSOLE] 🔙 Ensuring return to main page...
[17:20:43] [STDOUT] 
✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
⠸ Processing command...
[17:20:43] [RICH_CONSOLE] ✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
[17:20:43] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:20:43] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:20:43] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:20:43] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:20:43] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:20:43] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:43] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:43] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:43] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:43] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:43] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:20:43] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:20:43] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:20:43] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:20:43] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:20:43] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:20:43] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:20:43] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:20:43] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:20:43] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:20:43] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:20:43] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:20:44] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠇ Processing command...
[17:20:44] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[17:20:44] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.40s
⠇ Processing command...
[17:20:44] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:20:44] [STDOUT] 
🔙 Back navigation attempt 1/5 (in main app)
⠇ Processing command...
[17:20:44] [RICH_CONSOLE] 🔙 Back navigation attempt 1/5 (in main app)
[17:20:50] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
⠋ Processing command...
[17:20:50] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
[17:20:50] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:20:50] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[17:20:55] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:20:55] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[17:20:55] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:20:55] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[17:21:00] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠧ Processing command...
[17:21:00] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[17:21:00] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[17:21:00] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[17:21:02] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[17:21:02] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[17:21:05] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠹ Processing command...
[17:21:05] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[17:21:13] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:21:13] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:21:13] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:21:13] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:21:13] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:21:13] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:13] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:13] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:13] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:13] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:13] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:13] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:21:13] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:21:13] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:21:13] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:21:13] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:21:13] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:21:13] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠹ Processing command...
[17:21:13] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠹ Processing command...
[17:21:13] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[17:21:13] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[17:21:13] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[17:21:13] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[17:21:13] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:21:13] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.45s
⠦ Processing command...
[17:21:13] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:21:13] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:21:13] [STDOUT] 
✅ Main app restarted and ready with 21 elements
⠦ Processing command...
[17:21:13] [RICH_CONSOLE] ✅ Main app restarted and ready with 21 elements
[17:21:13] [STDOUT] 
✅ Main app restarted successfully
⠦ Processing command...
[17:21:13] [RICH_CONSOLE] ✅ Main app restarted successfully
[17:21:18] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠙ Processing command...
[17:21:18] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:21:18] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠙ Processing command...
[17:21:18] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠙ Processing command...
[17:21:18] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠙ Processing command...
[17:21:18] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[17:21:18] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[17:21:18] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:21:18] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:21:18] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠹ Processing command...
[17:21:18] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠹ Processing command...
[17:21:18] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠹ Processing command...
[17:21:18] [STDOUT] 
✓ Classified 21 elements instantly
⠹ Processing command...
[17:21:18] [STDOUT] 
✓ Headless discovery found 21 elements
⠹ Processing command...
[17:21:18] [STDOUT] 
✅ Headless discovery: 21 elements
⠹ Processing command...
[17:21:18] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠹ Processing command...
[17:21:18] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠹ Processing command...
[17:21:18] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠼ Processing command...
[17:21:18] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠼ Processing command...
[17:21:18] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠼ Processing command...
[17:21:18] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠼ Processing command...
[17:21:18] [STDOUT] 
🎯 Performing targeted visual scan...
⠼ Processing command...
[17:21:19] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[17:21:19] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[17:21:19] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.49s
⠧ Processing command...
[17:21:19] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:19] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:21:19] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:21:19] [STDOUT] 
✅ Main page verified - found indicator: ruang
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] ✅ Main page verified - found indicator: ruang
[17:21:19] [STDOUT] 
✅ VERIFIED: On main page, ready for next feature
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] ✅ VERIFIED: On main page, ready for next feature
[17:21:19] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠧ Processing command...
[17:21:19] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:21:24] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:21:24] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:21:24] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:21:24] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:21:24] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:21:24] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:24] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:24] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:24] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:24] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:24] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:21:24] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:21:24] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:21:24] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:21:24] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:21:24] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:21:24] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:21:24] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠸ Processing command...
[17:21:24] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠸ Processing command...
[17:21:24] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[17:21:24] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[17:21:24] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[17:21:24] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:21:24] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:21:24] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.68s
⠏ Processing command...
[17:21:24] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:21:24] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:21:24] [STDOUT] 
⚠️ No interactive elements found, but device appears responsive
⠏ Processing command...
[17:21:24] [RICH_CONSOLE] ⚠️ No interactive elements found, but device appears responsive
[17:21:24] [STDOUT] 
✅ Terminal-emulator sync verified, ready for next feature
⠏ Processing command...
[17:21:24] [RICH_CONSOLE] ✅ Terminal-emulator sync verified, ready for next feature
[17:21:24] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠏ Processing command...
[17:21:24] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:21:24] [STDOUT] 
🔍 Verifying terminal-emulator state consistency...
⠏ Processing command...
[17:21:24] [RICH_CONSOLE] 🔍 Verifying terminal-emulator state consistency...
[17:21:29] [STDOUT] 
✅ App package consistent: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[17:21:29] [RICH_CONSOLE] ✅ App package consistent: com.kemendikdasmen.rumahpendidikan
[17:21:29] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:21:29] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:21:29] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:21:29] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:21:29] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:21:30] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:21:30] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:21:30] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:21:30] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:21:30] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:21:30] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:21:30] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:21:30] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:21:30] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:21:30] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:21:30] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:21:30] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:21:30] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:21:30] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:21:30] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:21:30] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:21:30] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:21:30] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[17:21:30] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[17:21:30] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.58s
⠋ Processing command...
[17:21:30] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠋ Processing command...
[17:21:30] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:21:30] [STDOUT] 
✅ UI elements accessible: 21 found
⠋ Processing command...
[17:21:30] [RICH_CONSOLE] ✅ UI elements accessible: 21 found
[17:21:35] [STDOUT] 
❌ App info timeout after 5s
⠹ Processing command...
[17:21:35] [RICH_CONSOLE] ❌ App info timeout after 5s
[17:21:35] [STDOUT] 
❌ App not responsive during consistency check
⠹ Processing command...
[17:21:35] [RICH_CONSOLE] ❌ App not responsive during consistency check
[17:21:35] [STDOUT] 
⚠️ Terminal-emulator sync issues detected
⠹ Processing command...
[17:21:35] [RICH_CONSOLE] ⚠️ Terminal-emulator sync issues detected
[17:21:35] [STDOUT] 
⏳ Brief pause for sync stabilization...
⠸ Processing command...
[17:21:35] [RICH_CONSOLE] ⏳ Brief pause for sync stabilization...
[17:21:38] [STDOUT] 
🎯 === ANALYZING FEATURE 4/5: 'Feature_3' (unknown) ===
⠋ Processing command...
[17:21:38] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 4/5: 'Feature_3' (unknown) ===
[17:21:38] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Feature_3' for maximum element discovery
⠋ Processing command...
[17:21:38] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Feature_3' for maximum element discovery
[17:21:38] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠋ Processing command...
[17:21:38] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[17:21:38] [STDOUT] 
🏥 Pre-feature health check for 'Feature_3'...
⠋ Processing command...
[17:21:38] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Feature_3'...
[17:21:38] [STDOUT] 
🏥 Checking application health...
⠋ Processing command...
[17:21:38] [RICH_CONSOLE] 🏥 Checking application health...
[17:21:43] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[17:21:43] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:21:43] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[17:21:43] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[17:21:43] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[17:21:43] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:21:43] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:21:43] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:21:43] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:21:43] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:21:43] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[17:21:43] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠦ Processing command...
[17:21:43] [STDOUT] 
✓ Classified 21 elements instantly
⠦ Processing command...
[17:21:43] [STDOUT] 
✓ Headless discovery found 21 elements
⠦ Processing command...
[17:21:43] [STDOUT] 
✅ Headless discovery: 21 elements
⠦ Processing command...
[17:21:43] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[17:21:43] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[17:21:43] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠦ Processing command...
[17:21:43] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠦ Processing command...
[17:21:43] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[17:21:43] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[17:21:43] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[17:21:44] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[17:21:44] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[17:21:44] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.42s
⠋ Processing command...
[17:21:44] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠋ Processing command...
[17:21:44] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:21:49] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠸ Processing command...
[17:21:49] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:21:54] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠧ Processing command...
[17:21:54] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:21:54] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠧ Processing command...
[17:21:54] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠧ Processing command...
[17:21:54] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠧ Processing command...
[17:21:54] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:21:54] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:21:54] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:21:54] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:21:54] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[17:21:54] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠇ Processing command...
[17:21:54] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠇ Processing command...
[17:21:54] [STDOUT] 
✓ Classified 21 elements instantly
⠇ Processing command...
[17:21:54] [STDOUT] 
✓ Headless discovery found 21 elements
⠇ Processing command...
[17:21:54] [STDOUT] 
✅ Headless discovery: 21 elements
⠇ Processing command...
[17:21:54] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠇ Processing command...
[17:21:54] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠇ Processing command...
[17:21:54] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠙ Processing command...
[17:21:54] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠙ Processing command...
[17:21:54] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[17:21:54] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[17:21:54] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[17:21:55] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[17:21:55] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[17:21:55] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.82s
⠧ Processing command...
[17:21:55] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:21:55] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:21:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:21:55] [STDOUT] 
✅ App is responsive
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] ✅ App is responsive
[17:21:55] [STDOUT] 
📊 Feature Feature_3 started:
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 📊 Feature Feature_3 started:
[17:21:55] [STDOUT] 
   Elements: 0
⠧ Processing command...
[17:21:55] [RICH_CONSOLE]    Elements: 0
[17:21:55] [STDOUT] 
   Interactions: 0
⠧ Processing command...
[17:21:55] [RICH_CONSOLE]    Interactions: 0
[17:21:55] [STDOUT] 
   Sub-features: 0
⠧ Processing command...
[17:21:55] [RICH_CONSOLE]    Sub-features: 0
[17:21:55] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 5.2min
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 5.2min
[17:21:55] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Feature_3'
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Feature_3'
[17:21:55] [STDOUT] 
❌ FAILED: Feature 'Feature_3': 'name'
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] ❌ FAILED: Feature 'Feature_3': 'name'
[17:21:55] [STDOUT] 
📊 Feature Feature_3 failed:
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 📊 Feature Feature_3 failed:
[17:21:55] [STDOUT] 
   Elements: 0
⠧ Processing command...
[17:21:55] [RICH_CONSOLE]    Elements: 0
[17:21:55] [STDOUT] 
   Interactions: 0
⠧ Processing command...
[17:21:55] [RICH_CONSOLE]    Interactions: 0
[17:21:55] [STDOUT] 
   Sub-features: 0
⠧ Processing command...
[17:21:55] [RICH_CONSOLE]    Sub-features: 0
[17:21:55] [STDOUT] 
🏥 Post-feature health check for 'Feature_3'...
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🏥 Post-feature health check for 'Feature_3'...
[17:21:55] [STDOUT] 
🏥 Checking application health...
⠧ Processing command...
[17:21:55] [RICH_CONSOLE] 🏥 Checking application health...
[17:22:00] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[17:22:00] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:22:00] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[17:22:00] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[17:22:00] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[17:22:00] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:22:00] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:22:00] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:22:00] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:22:00] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[17:22:00] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[17:22:00] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠸ Processing command...
[17:22:00] [STDOUT] 
✓ Classified 21 elements instantly
⠸ Processing command...
[17:22:00] [STDOUT] 
✓ Headless discovery found 21 elements
⠸ Processing command...
[17:22:00] [STDOUT] 
✅ Headless discovery: 21 elements
⠸ Processing command...
[17:22:00] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[17:22:00] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[17:22:00] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠸ Processing command...
[17:22:00] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠸ Processing command...
[17:22:00] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[17:22:00] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[17:22:00] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[17:22:00] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[17:22:00] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[17:22:00] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.62s
⠋ Processing command...
[17:22:00] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠋ Processing command...
[17:22:00] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:22:06] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠸ Processing command...
[17:22:06] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:22:11] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:22:11] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:22:11] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:22:11] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:22:11] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:22:11] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:22:11] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:22:11] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:22:11] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:22:11] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[17:22:11] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:22:11] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠏ Processing command...
[17:22:11] [STDOUT] 
✓ Classified 21 elements instantly
⠏ Processing command...
[17:22:11] [STDOUT] 
✓ Headless discovery found 21 elements
⠏ Processing command...
[17:22:11] [STDOUT] 
✅ Headless discovery: 21 elements
⠏ Processing command...
[17:22:11] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:22:11] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:22:11] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:22:11] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:22:11] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:22:11] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:22:11] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:22:11] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:22:11] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:22:11] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.49s
⠹ Processing command...
[17:22:11] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:11] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:22:11] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:22:11] [STDOUT] 
✅ App is responsive
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] ✅ App is responsive
[17:22:11] [STDOUT] 
🔄 CRITICAL: Ensuring return to main page before next feature...
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🔄 CRITICAL: Ensuring return to main page before next feature...
[17:22:11] [STDOUT] 
🔙 Ensuring return to main page...
⠸ Processing command...
[17:22:11] [RICH_CONSOLE] 🔙 Ensuring return to main page...
[17:22:16] [STDOUT] 
✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
⠦ Processing command...
[17:22:16] [RICH_CONSOLE] ✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
[17:22:16] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:22:16] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:22:16] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:22:16] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:22:16] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:22:16] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:16] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:16] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:16] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:16] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:16] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:16] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠧ Processing command...
[17:22:16] [STDOUT] 
✓ Classified 21 elements instantly
⠧ Processing command...
[17:22:16] [STDOUT] 
✓ Headless discovery found 21 elements
⠧ Processing command...
[17:22:16] [STDOUT] 
✅ Headless discovery: 21 elements
⠧ Processing command...
[17:22:16] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[17:22:16] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:22:16] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠧ Processing command...
[17:22:16] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠧ Processing command...
[17:22:16] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[17:22:16] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[17:22:16] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[17:22:17] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠙ Processing command...
[17:22:17] [STDOUT] 
✅ Visual validation: additional elements found
⠙ Processing command...
[17:22:17] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.37s
⠙ Processing command...
[17:22:17] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:17] [STDOUT] 
🔙 Back navigation attempt 1/5 (in main app)
⠙ Processing command...
[17:22:17] [RICH_CONSOLE] 🔙 Back navigation attempt 1/5 (in main app)
[17:22:23] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
⠸ Processing command...
[17:22:23] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
[17:22:23] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[17:22:23] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[17:22:28] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[17:22:28] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[17:22:28] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[17:22:28] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[17:22:33] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠏ Processing command...
[17:22:33] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[17:22:33] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[17:22:33] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[17:22:35] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠴ Processing command...
[17:22:35] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[17:22:38] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:22:38] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[17:22:46] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:22:46] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:22:46] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:22:46] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:22:46] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:22:46] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:22:46] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:22:46] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:22:46] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:22:46] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:22:46] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:22:46] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:22:46] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:22:46] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:22:46] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:22:46] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:22:46] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:22:46] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:22:46] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:22:46] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:22:46] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:22:46] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:22:46] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠇ Processing command...
[17:22:46] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[17:22:46] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.41s
⠇ Processing command...
[17:22:46] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠇ Processing command...
[17:22:46] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:22:46] [STDOUT] 
✅ Main app restarted and ready with 21 elements
⠇ Processing command...
[17:22:46] [RICH_CONSOLE] ✅ Main app restarted and ready with 21 elements
[17:22:46] [STDOUT] 
✅ Main app restarted successfully
⠇ Processing command...
[17:22:46] [RICH_CONSOLE] ✅ Main app restarted successfully
[17:22:51] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:22:51] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:22:51] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:22:51] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:22:51] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:22:51] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[17:22:51] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[17:22:51] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[17:22:51] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[17:22:51] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠴ Processing command...
[17:22:51] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠴ Processing command...
[17:22:51] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠴ Processing command...
[17:22:51] [STDOUT] 
✓ Classified 21 elements instantly
⠴ Processing command...
[17:22:51] [STDOUT] 
✓ Headless discovery found 21 elements
⠴ Processing command...
[17:22:51] [STDOUT] 
✅ Headless discovery: 21 elements
⠴ Processing command...
[17:22:51] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠴ Processing command...
[17:22:51] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠴ Processing command...
[17:22:51] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠦ Processing command...
[17:22:51] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠦ Processing command...
[17:22:51] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[17:22:51] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[17:22:51] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[17:22:52] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠙ Processing command...
[17:22:52] [STDOUT] 
✅ Visual validation: additional elements found
⠙ Processing command...
[17:22:52] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.69s
⠙ Processing command...
[17:22:52] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:22:52] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:22:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:22:52] [STDOUT] 
✅ Main page verified - found indicator: ruang
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] ✅ Main page verified - found indicator: ruang
[17:22:52] [STDOUT] 
✅ VERIFIED: On main page, ready for next feature
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] ✅ VERIFIED: On main page, ready for next feature
[17:22:52] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠹ Processing command...
[17:22:52] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:22:57] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[17:22:57] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:22:57] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[17:22:57] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[17:22:57] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[17:22:57] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:57] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:57] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:57] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:57] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:57] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[17:22:57] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠧ Processing command...
[17:22:57] [STDOUT] 
✓ Classified 21 elements instantly
⠧ Processing command...
[17:22:57] [STDOUT] 
✓ Headless discovery found 21 elements
⠧ Processing command...
[17:22:57] [STDOUT] 
✅ Headless discovery: 21 elements
⠧ Processing command...
[17:22:57] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[17:22:57] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:22:57] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠇ Processing command...
[17:22:57] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠇ Processing command...
[17:22:57] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[17:22:57] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[17:22:57] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[17:22:58] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[17:22:58] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:22:58] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.88s
⠦ Processing command...
[17:22:58] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:22:58] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:22:58] [STDOUT] 
⚠️ No interactive elements found, but device appears responsive
⠦ Processing command...
[17:22:58] [RICH_CONSOLE] ⚠️ No interactive elements found, but device appears responsive
[17:22:58] [STDOUT] 
✅ Terminal-emulator sync verified, ready for next feature
⠦ Processing command...
[17:22:58] [RICH_CONSOLE] ✅ Terminal-emulator sync verified, ready for next feature
[17:22:58] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠦ Processing command...
[17:22:58] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:22:58] [STDOUT] 
🔍 Verifying terminal-emulator state consistency...
⠦ Processing command...
[17:22:58] [RICH_CONSOLE] 🔍 Verifying terminal-emulator state consistency...
[17:23:03] [STDOUT] 
✅ App package consistent: com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[17:23:03] [RICH_CONSOLE] ✅ App package consistent: com.kemendikdasmen.rumahpendidikan
[17:23:03] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:23:03] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:23:03] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:23:03] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[17:23:03] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[17:23:03] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:03] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:03] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:03] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:03] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:03] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:03] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:23:03] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:23:03] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:23:03] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:23:03] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:23:03] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:23:03] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠸ Processing command...
[17:23:03] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠸ Processing command...
[17:23:03] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[17:23:03] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[17:23:03] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[17:23:04] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:23:04] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:23:04] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.76s
⠏ Processing command...
[17:23:04] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:23:04] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:23:04] [STDOUT] 
✅ UI elements accessible: 21 found
⠏ Processing command...
[17:23:04] [RICH_CONSOLE] ✅ UI elements accessible: 21 found
[17:23:09] [STDOUT] 
❌ App info timeout after 5s
⠙ Processing command...
[17:23:09] [RICH_CONSOLE] ❌ App info timeout after 5s
[17:23:09] [STDOUT] 
❌ App not responsive during consistency check
⠙ Processing command...
[17:23:09] [RICH_CONSOLE] ❌ App not responsive during consistency check
[17:23:09] [STDOUT] 
⚠️ Terminal-emulator sync issues detected
⠙ Processing command...
[17:23:09] [RICH_CONSOLE] ⚠️ Terminal-emulator sync issues detected
[17:23:09] [STDOUT] 
⏳ Brief pause for sync stabilization...
⠙ Processing command...
[17:23:09] [RICH_CONSOLE] ⏳ Brief pause for sync stabilization...
[17:23:12] [STDOUT] 
🎯 === ANALYZING FEATURE 5/5: 'Feature_4' (unknown) ===
⠏ Processing command...
[17:23:12] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 5/5: 'Feature_4' (unknown) ===
[17:23:12] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Feature_4' for maximum element discovery
⠏ Processing command...
[17:23:12] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Feature_4' for maximum element discovery
[17:23:12] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠏ Processing command...
[17:23:12] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[17:23:12] [STDOUT] 
🏥 Pre-feature health check for 'Feature_4'...
⠏ Processing command...
[17:23:12] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Feature_4'...
[17:23:12] [STDOUT] 
🏥 Checking application health...
⠏ Processing command...
[17:23:12] [RICH_CONSOLE] 🏥 Checking application health...
[17:23:17] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:23:17] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:23:17] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:23:17] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:23:17] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:23:17] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:23:17] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:23:17] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:23:17] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:23:17] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:23:17] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:23:17] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:23:17] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:23:17] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:23:17] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:23:17] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:23:17] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:23:17] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠼ Processing command...
[17:23:17] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠼ Processing command...
[17:23:17] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠼ Processing command...
[17:23:17] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠼ Processing command...
[17:23:17] [STDOUT] 
🎯 Performing targeted visual scan...
⠼ Processing command...
[17:23:17] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:23:17] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:23:17] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.49s
⠏ Processing command...
[17:23:17] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:23:17] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:23:22] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠸ Processing command...
[17:23:22] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:23:27] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:23:27] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:23:27] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:23:27] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:23:27] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:23:28] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:28] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:28] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:28] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:28] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:28] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:23:28] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠏ Processing command...
[17:23:28] [STDOUT] 
✓ Classified 21 elements instantly
⠏ Processing command...
[17:23:28] [STDOUT] 
✓ Headless discovery found 21 elements
⠏ Processing command...
[17:23:28] [STDOUT] 
✅ Headless discovery: 21 elements
⠏ Processing command...
[17:23:28] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:23:28] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:23:28] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:23:28] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:23:28] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:23:28] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:23:28] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:23:28] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:23:28] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:23:28] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.48s
⠹ Processing command...
[17:23:28] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:28] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:23:28] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:23:28] [STDOUT] 
✅ App is responsive
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] ✅ App is responsive
[17:23:28] [STDOUT] 
📊 Feature Feature_4 started:
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 📊 Feature Feature_4 started:
[17:23:28] [STDOUT] 
   Elements: 0
⠹ Processing command...
[17:23:28] [RICH_CONSOLE]    Elements: 0
[17:23:28] [STDOUT] 
   Interactions: 0
⠹ Processing command...
[17:23:28] [RICH_CONSOLE]    Interactions: 0
[17:23:28] [STDOUT] 
   Sub-features: 0
⠹ Processing command...
[17:23:28] [RICH_CONSOLE]    Sub-features: 0
[17:23:28] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 6.7min
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 6.7min
[17:23:28] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Feature_4'
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Feature_4'
[17:23:28] [STDOUT] 
❌ FAILED: Feature 'Feature_4': 'name'
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] ❌ FAILED: Feature 'Feature_4': 'name'
[17:23:28] [STDOUT] 
📊 Feature Feature_4 failed:
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 📊 Feature Feature_4 failed:
[17:23:28] [STDOUT] 
   Elements: 0
⠹ Processing command...
[17:23:28] [RICH_CONSOLE]    Elements: 0
[17:23:28] [STDOUT] 
   Interactions: 0
⠹ Processing command...
[17:23:28] [RICH_CONSOLE]    Interactions: 0
[17:23:28] [STDOUT] 
   Sub-features: 0
⠹ Processing command...
[17:23:28] [RICH_CONSOLE]    Sub-features: 0
[17:23:28] [STDOUT] 
🏥 Post-feature health check for 'Feature_4'...
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🏥 Post-feature health check for 'Feature_4'...
[17:23:28] [STDOUT] 
🏥 Checking application health...
⠹ Processing command...
[17:23:28] [RICH_CONSOLE] 🏥 Checking application health...
[17:23:33] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:23:33] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:23:33] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:23:33] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:23:33] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:23:33] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:33] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:33] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:33] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:33] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:33] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠇ Processing command...
[17:23:33] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠇ Processing command...
[17:23:33] [STDOUT] 
✓ Classified 21 elements instantly
⠇ Processing command...
[17:23:33] [STDOUT] 
✓ Headless discovery found 21 elements
⠇ Processing command...
[17:23:33] [STDOUT] 
✅ Headless discovery: 21 elements
⠇ Processing command...
[17:23:33] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠇ Processing command...
[17:23:33] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠇ Processing command...
[17:23:33] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠙ Processing command...
[17:23:33] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠙ Processing command...
[17:23:33] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[17:23:33] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[17:23:33] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[17:23:34] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:23:34] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:23:34] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.73s
⠦ Processing command...
[17:23:34] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:23:34] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:23:39] [STDOUT] 
✅ UI elements accessible: 21 elements found
⠏ Processing command...
[17:23:39] [RICH_CONSOLE] ✅ UI elements accessible: 21 elements found
[17:23:44] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[17:23:44] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:23:44] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[17:23:44] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[17:23:44] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[17:23:44] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:23:44] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:23:44] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:23:44] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:23:44] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[17:23:44] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[17:23:44] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠸ Processing command...
[17:23:44] [STDOUT] 
✓ Classified 21 elements instantly
⠸ Processing command...
[17:23:44] [STDOUT] 
✓ Headless discovery found 21 elements
⠸ Processing command...
[17:23:44] [STDOUT] 
✅ Headless discovery: 21 elements
⠸ Processing command...
[17:23:44] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[17:23:44] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[17:23:44] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠸ Processing command...
[17:23:44] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠸ Processing command...
[17:23:44] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[17:23:44] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[17:23:44] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[17:23:44] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[17:23:44] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:23:44] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.32s
⠦ Processing command...
[17:23:44] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:23:44] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠦ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:23:44] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[17:23:44] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:23:44] [STDOUT] 
✅ App is responsive
⠧ Processing command...
[17:23:44] [RICH_CONSOLE] ✅ App is responsive
[17:23:44] [STDOUT] 
🔄 CRITICAL: Ensuring return to main page before next feature...
⠧ Processing command...
[17:23:44] [RICH_CONSOLE] 🔄 CRITICAL: Ensuring return to main page before next feature...
[17:23:44] [STDOUT] 
🔙 Ensuring return to main page...
⠧ Processing command...
[17:23:44] [RICH_CONSOLE] 🔙 Ensuring return to main page...
[17:23:49] [STDOUT] 
✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
⠋ Processing command...
[17:23:49] [RICH_CONSOLE] ✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
[17:23:49] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:23:49] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:23:49] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:23:49] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:23:49] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:23:49] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:49] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:49] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:49] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:49] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:49] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:23:49] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:23:49] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:23:49] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:23:49] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:23:49] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:23:49] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:23:50] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠸ Processing command...
[17:23:50] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠸ Processing command...
[17:23:50] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[17:23:50] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[17:23:50] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[17:23:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[17:23:50] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:23:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.47s
⠦ Processing command...
[17:23:50] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:23:50] [STDOUT] 
🔙 Back navigation attempt 1/5 (in main app)
⠦ Processing command...
[17:23:50] [RICH_CONSOLE] 🔙 Back navigation attempt 1/5 (in main app)
[17:23:56] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
⠇ Processing command...
[17:23:56] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
[17:23:56] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠇ Processing command...
[17:23:56] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[17:24:01] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[17:24:01] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[17:24:01] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[17:24:01] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[17:24:06] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠼ Processing command...
[17:24:06] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[17:24:06] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠼ Processing command...
[17:24:06] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[17:24:08] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:24:08] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[17:24:12] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[17:24:12] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[17:24:19] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:24:19] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:19] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:24:19] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:24:19] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:24:19] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:19] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:19] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:19] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:19] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:19] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:19] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:24:19] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:24:19] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:24:19] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:24:19] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:24:19] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:24:19] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠙ Processing command...
[17:24:19] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠙ Processing command...
[17:24:19] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[17:24:19] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[17:24:19] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[17:24:19] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:24:19] [STDOUT] 
✅ Visual validation: additional elements found
⠴ Processing command...
[17:24:19] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.52s
⠴ Processing command...
[17:24:19] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠴ Processing command...
[17:24:19] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:19] [STDOUT] 
✅ Main app restarted and ready with 21 elements
⠴ Processing command...
[17:24:19] [RICH_CONSOLE] ✅ Main app restarted and ready with 21 elements
[17:24:19] [STDOUT] 
✅ Main app restarted successfully
⠴ Processing command...
[17:24:19] [RICH_CONSOLE] ✅ Main app restarted successfully
[17:24:25] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[17:24:25] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:25] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[17:24:25] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[17:24:25] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[17:24:25] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:25] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:25] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:25] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:25] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:25] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:25] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠸ Processing command...
[17:24:25] [STDOUT] 
✓ Classified 21 elements instantly
⠸ Processing command...
[17:24:25] [STDOUT] 
✓ Headless discovery found 21 elements
⠸ Processing command...
[17:24:25] [STDOUT] 
✅ Headless discovery: 21 elements
⠸ Processing command...
[17:24:25] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[17:24:25] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[17:24:25] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:24:25] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:24:25] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:24:25] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:24:25] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:24:25] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:24:25] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:24:25] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.60s
⠏ Processing command...
[17:24:25] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:24:25] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:25] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:25] [STDOUT] 
✅ Main page verified - found indicator: ruang
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] ✅ Main page verified - found indicator: ruang
[17:24:25] [STDOUT] 
✅ VERIFIED: On main page, ready for next feature
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] ✅ VERIFIED: On main page, ready for next feature
[17:24:25] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠋ Processing command...
[17:24:25] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:24:30] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:24:30] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:30] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:24:30] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:24:30] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:24:30] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:30] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:30] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:30] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:30] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:30] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:30] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:24:30] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:24:30] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:24:30] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:24:30] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:24:30] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:24:31] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠧ Processing command...
[17:24:31] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠧ Processing command...
[17:24:31] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[17:24:31] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[17:24:31] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[17:24:31] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:24:31] [STDOUT] 
✅ Visual validation: additional elements found
⠴ Processing command...
[17:24:31] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.96s
⠴ Processing command...
[17:24:31] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠴ Processing command...
[17:24:31] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:31] [STDOUT] 
⚠️ No interactive elements found, but device appears responsive
⠴ Processing command...
[17:24:31] [RICH_CONSOLE] ⚠️ No interactive elements found, but device appears responsive
[17:24:31] [STDOUT] 
✅ Terminal-emulator sync verified, ready for next feature
⠴ Processing command...
[17:24:31] [RICH_CONSOLE] ✅ Terminal-emulator sync verified, ready for next feature
[17:24:31] [STDOUT] 
🔄 Ensuring terminal-emulator sync before next feature...
⠴ Processing command...
[17:24:31] [RICH_CONSOLE] 🔄 Ensuring terminal-emulator sync before next feature...
[17:24:31] [STDOUT] 
🔍 Verifying terminal-emulator state consistency...
⠴ Processing command...
[17:24:31] [RICH_CONSOLE] 🔍 Verifying terminal-emulator state consistency...
[17:24:36] [STDOUT] 
✅ App package consistent: com.kemendikdasmen.rumahpendidikan
⠇ Processing command...
[17:24:36] [RICH_CONSOLE] ✅ App package consistent: com.kemendikdasmen.rumahpendidikan
[17:24:36] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:24:36] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:36] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:24:36] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:24:36] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:24:36] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:36] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:36] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:36] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:36] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:36] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:36] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠋ Processing command...
[17:24:36] [STDOUT] 
✓ Classified 21 elements instantly
⠋ Processing command...
[17:24:36] [STDOUT] 
✓ Headless discovery found 21 elements
⠋ Processing command...
[17:24:36] [STDOUT] 
✅ Headless discovery: 21 elements
⠋ Processing command...
[17:24:36] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:24:36] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:24:37] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠹ Processing command...
[17:24:37] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠹ Processing command...
[17:24:37] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[17:24:37] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[17:24:37] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[17:24:37] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[17:24:37] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[17:24:37] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.70s
⠧ Processing command...
[17:24:37] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠧ Processing command...
[17:24:37] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:37] [STDOUT] 
✅ UI elements accessible: 21 found
⠧ Processing command...
[17:24:37] [RICH_CONSOLE] ✅ UI elements accessible: 21 found
[17:24:42] [STDOUT] 
❌ App info timeout after 5s
⠏ Processing command...
[17:24:42] [RICH_CONSOLE] ❌ App info timeout after 5s
[17:24:42] [STDOUT] 
❌ App not responsive during consistency check
⠏ Processing command...
[17:24:42] [RICH_CONSOLE] ❌ App not responsive during consistency check
[17:24:42] [STDOUT] 
⚠️ Terminal-emulator sync issues detected
⠏ Processing command...
[17:24:42] [RICH_CONSOLE] ⚠️ Terminal-emulator sync issues detected
[17:24:42] [STDOUT] 
⏳ Brief pause for sync stabilization...
⠏ Processing command...
[17:24:42] [RICH_CONSOLE] ⏳ Brief pause for sync stabilization...
[17:24:45] [STDOUT] 
📊 COMPREHENSIVE FEATURE ANALYSIS SUMMARY:
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 
📊 COMPREHENSIVE FEATURE ANALYSIS SUMMARY:
[17:24:45] [STDOUT] 
• Total features analyzed: 0/5
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] • Total features analyzed: 0/5
[17:24:45] [STDOUT] 
• Fully completed features: 0/5
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] • Fully completed features: 0/5
[17:24:45] [STDOUT] 
• Failed features: 5
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] • Failed features: 5
[17:24:45] [STDOUT] 
📋 Phase 3: Final feature verification and cleanup...
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 
📋 Phase 3: Final feature verification and cleanup...
[17:24:45] [STDOUT] 
🔍 Performing final feature verification...
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 🔍 Performing final feature verification...
[17:24:45] [STDOUT] 
📊 Verification complete: 0 verified, 5 issues
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 📊 Verification complete: 0 verified, 5 issues
[17:24:45] [STDOUT] 
  ⚠️ unknown (unknown) - Not analyzed
⠧ Processing command...
[17:24:45] [RICH_CONSOLE]   ⚠️ unknown (unknown) - Not analyzed
[17:24:45] [STDOUT] 
  ⚠️ unknown (unknown) - Not analyzed
⠧ Processing command...
[17:24:45] [RICH_CONSOLE]   ⚠️ unknown (unknown) - Not analyzed
[17:24:45] [STDOUT] 
  ⚠️ unknown (unknown) - Not analyzed
⠧ Processing command...
[17:24:45] [RICH_CONSOLE]   ⚠️ unknown (unknown) - Not analyzed
[17:24:45] [STDOUT] 
  ⚠️ unknown (unknown) - Not analyzed
⠧ Processing command...
[17:24:45] [RICH_CONSOLE]   ⚠️ unknown (unknown) - Not analyzed
[17:24:45] [STDOUT] 
  ⚠️ unknown (unknown) - Not analyzed
⠧ Processing command...
[17:24:45] [RICH_CONSOLE]   ⚠️ unknown (unknown) - Not analyzed
[17:24:45] [STDOUT] 
✅ Comprehensive feature-based analysis completed!
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] ✅ Comprehensive feature-based analysis completed!
[17:24:45] [STDOUT] 
📊 Features analyzed: 0/5
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 📊 Features analyzed: 0/5
[17:24:45] [STDOUT] 
📊 Verification results: 0 verified
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 📊 Verification results: 0 verified
[17:24:45] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 8.0min
⠧ Processing command...
[17:24:45] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 8.0min
[17:24:48] [STDOUT] 
🔍 Real-time problem monitoring stopped
⠋ Processing command...
[17:24:48] [STDOUT] 
🔧 Real-time problem solver monitoring stopped
⠋ Processing command...
[17:24:48] [RICH_CONSOLE] 🔧 Real-time problem solver monitoring stopped
[17:24:48] [STDOUT] 
🎯 Performance Goals Summary:
⠋ Processing command...
[17:24:48] [RICH_CONSOLE] 🎯 Performance Goals Summary:
[17:24:48] [STDOUT] 
  • Elements: ❌ 0/3000
⠋ Processing command...
[17:24:48] [RICH_CONSOLE]   • Elements: ❌ 0/3000
[17:24:48] [STDOUT] 
  • Duration: ✅ 8.0/60 minutes
⠋ Processing command...
[17:24:48] [RICH_CONSOLE]   • Duration: ✅ 8.0/60 minutes
[17:24:48] [STDOUT] 
  • Overall: ⚠️ NEEDS IMPROVEMENT
⠋ Processing command...
[17:24:48] [RICH_CONSOLE]   • Overall: ⚠️ NEEDS IMPROVEMENT
[17:24:48] [STDOUT] 
📋 Phase 4: Verifying analysis completion...
⠋ Processing command...
[17:24:48] [RICH_CONSOLE] 📋 Phase 4: Verifying analysis completion...
[17:24:48] [STDOUT] 
📊 FEATURE ANALYSIS COMPLETION VERIFICATION:
⠋ Processing command...
[17:24:48] [RICH_CONSOLE] 📊 FEATURE ANALYSIS COMPLETION VERIFICATION:
[17:24:48] [STDOUT] 
   Features analyzed: 0/5 (0.0%)
⠋ Processing command...
[17:24:48] [RICH_CONSOLE]    Features analyzed: 0/5 (0.0%)
[17:24:49] [STDOUT] 
🎯 100% COMPLETION REQUIREMENT: All features must be analyzed
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🎯 100% COMPLETION REQUIREMENT: All features must be analyzed
[17:24:49] [STDOUT] 
🔍 MANDATORY STRUCTURE CHECK: Verifying element coverage...
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🔍 MANDATORY STRUCTURE CHECK: Verifying element coverage...
[17:24:49] [STDOUT] 
🔍 Verifying mandatory structure elements coverage...
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🔍 Verifying mandatory structure elements coverage...
[17:24:49] [STDOUT] 
📊 Structure Coverage: 100.0%
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 📊 Structure Coverage: 100.0%
[17:24:49] [STDOUT] 
📊 Missing Elements: 0
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 📊 Missing Elements: 0
[17:24:49] [STDOUT] 
✅ 100% STRUCTURE COVERAGE ACHIEVED!
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] ✅ 100% STRUCTURE COVERAGE ACHIEVED!
[17:24:49] [STDOUT] 
✅ 100% Structure coverage achieved!
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] ✅ 100% Structure coverage achieved!
[17:24:49] [STDOUT] 
🎯 With complete structure coverage, allowing flexible feature completion threshold: 80.0%
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🎯 With complete structure coverage, allowing flexible feature completion threshold: 80.0%
[17:24:49] [STDOUT] 
❌ Feature analysis critically low: 0.0% < 15.0%
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] ❌ Feature analysis critically low: 0.0% < 15.0%
[17:24:49] [STDOUT] 
⚠️ Missing 5 features - analysis should continue
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] ⚠️ Missing 5 features - analysis should continue
[17:24:49] [STDOUT] 
⚠️ Analysis may be incomplete. Application remains open for manual verification.
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] ⚠️ Analysis may be incomplete. Application remains open for manual verification.
[17:24:49] [STDOUT] 
🔄 ANALYSIS MUST CONTINUE - Restarting analysis to achieve 100% coverage
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🔄 ANALYSIS MUST CONTINUE - Restarting analysis to achieve 100% coverage
[17:24:49] [STDOUT] 
🎯 Performing targeted analysis for missing mandatory elements...
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🎯 Performing targeted analysis for missing mandatory elements...
[17:24:49] [STDOUT] 
🎯 TARGETED MISSING ELEMENTS ANALYSIS
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🎯 TARGETED MISSING ELEMENTS ANALYSIS
[17:24:49] [STDOUT] 
🔍 Focusing on specific missing elements from structure requirements
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🔍 Focusing on specific missing elements from structure requirements
[17:24:49] [STDOUT] 
🔍 Target 1: Search button (magnifying glass icon)
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🔍 Target 1: Search button (magnifying glass icon)
[17:24:49] [STDOUT] 
🔍 Searching for magnifying glass search button...
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🔍 Searching for magnifying glass search button...
[17:24:49] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:24:49] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:49] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:24:49] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:24:49] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:24:49] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:49] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:49] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:49] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:49] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:49] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:49] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:24:49] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:24:49] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:24:49] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:24:49] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:24:49] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:24:49] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠙ Processing command...
[17:24:49] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠙ Processing command...
[17:24:49] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[17:24:49] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[17:24:49] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[17:24:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[17:24:50] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[17:24:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.37s
⠼ Processing command...
[17:24:50] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠼ Processing command...
[17:24:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🔍 Target 2: Missing Ruang GTK elements
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🔍 Target 2: Missing Ruang GTK elements
[17:24:50] [STDOUT] 
🔍 Targeting missing Ruang GTK elements...
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🔍 Targeting missing Ruang GTK elements...
[17:24:50] [STDOUT] 
🧭 Navigating to section: Ruang GTK
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🧭 Navigating to section: Ruang GTK
[17:24:50] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[17:24:50] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:50] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[17:24:50] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:50] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠦ Processing command...
[17:24:50] [STDOUT] 
✓ Classified 21 elements instantly
⠦ Processing command...
[17:24:50] [STDOUT] 
✓ Headless discovery found 21 elements
⠦ Processing command...
[17:24:50] [STDOUT] 
✅ Headless discovery: 21 elements
⠦ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[17:24:50] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[17:24:50] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠦ Processing command...
[17:24:50] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠦ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[17:24:50] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[17:24:50] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[17:24:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[17:24:50] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[17:24:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.35s
⠏ Processing command...
[17:24:50] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[17:24:50] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:50] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:24:50] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[17:24:50] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠋ Processing command...
[17:24:50] [STDOUT] 
✓ Classified 21 elements instantly
⠋ Processing command...
[17:24:50] [STDOUT] 
✓ Headless discovery found 21 elements
⠋ Processing command...
[17:24:50] [STDOUT] 
✅ Headless discovery: 21 elements
⠋ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[17:24:50] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[17:24:50] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:24:50] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:24:50] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:24:50] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:24:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠸ Processing command...
[17:24:50] [STDOUT] 
✅ Visual validation: additional elements found
⠸ Processing command...
[17:24:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.28s
⠸ Processing command...
[17:24:50] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:50] [STDOUT] 
🔍 Target 3: Missing navigation menu elements
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🔍 Target 3: Missing navigation menu elements
[17:24:50] [STDOUT] 
🔍 Targeting missing navigation elements...
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🔍 Targeting missing navigation elements...
[17:24:50] [STDOUT] 
🍔 Trying to access navigation menu...
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🍔 Trying to access navigation menu...
[17:24:50] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:24:50] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:50] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:24:50] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:50] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:50] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠸ Processing command...
[17:24:50] [STDOUT] 
✓ Classified 21 elements instantly
⠸ Processing command...
[17:24:50] [STDOUT] 
✓ Headless discovery found 21 elements
⠸ Processing command...
[17:24:50] [STDOUT] 
✅ Headless discovery: 21 elements
⠸ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[17:24:50] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[17:24:50] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠼ Processing command...
[17:24:50] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠼ Processing command...
[17:24:50] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠼ Processing command...
[17:24:50] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠼ Processing command...
[17:24:50] [STDOUT] 
🎯 Performing targeted visual scan...
⠼ Processing command...
[17:24:51] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[17:24:51] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[17:24:51] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.34s
⠇ Processing command...
[17:24:51] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:24:51] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:51] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:24:51] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠇ Processing command...
[17:24:51] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠇ Processing command...
[17:24:51] [STDOUT] 
✓ Classified 21 elements instantly
⠇ Processing command...
[17:24:51] [STDOUT] 
✓ Headless discovery found 21 elements
⠇ Processing command...
[17:24:51] [STDOUT] 
✅ Headless discovery: 21 elements
⠇ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠇ Processing command...
[17:24:51] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠇ Processing command...
[17:24:51] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠇ Processing command...
[17:24:51] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠇ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[17:24:51] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[17:24:51] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[17:24:51] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:24:51] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:24:51] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.31s
⠹ Processing command...
[17:24:51] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🔍 Target 4: Other missing section elements
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🔍 Target 4: Other missing section elements
[17:24:51] [STDOUT] 
🔍 Targeting other missing elements...
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🔍 Targeting other missing elements...
[17:24:51] [STDOUT] 
🧭 Navigating to section: ruang_pemerintah
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🧭 Navigating to section: ruang_pemerintah
[17:24:51] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[17:24:51] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:51] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[17:24:51] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠹ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠹ Processing command...
[17:24:51] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠹ Processing command...
[17:24:51] [STDOUT] 
✓ Classified 21 elements instantly
⠹ Processing command...
[17:24:51] [STDOUT] 
✓ Headless discovery found 21 elements
⠹ Processing command...
[17:24:51] [STDOUT] 
✅ Headless discovery: 21 elements
⠹ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠹ Processing command...
[17:24:51] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠹ Processing command...
[17:24:51] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠹ Processing command...
[17:24:51] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠹ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[17:24:51] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[17:24:51] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[17:24:51] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:24:51] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[17:24:51] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.28s
⠦ Processing command...
[17:24:51] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:51] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:24:51] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:51] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:24:51] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:51] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:51] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠦ Processing command...
[17:24:51] [STDOUT] 
✓ Classified 21 elements instantly
⠦ Processing command...
[17:24:51] [STDOUT] 
✓ Headless discovery found 21 elements
⠦ Processing command...
[17:24:51] [STDOUT] 
✅ Headless discovery: 21 elements
⠦ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[17:24:51] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[17:24:51] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠦ Processing command...
[17:24:51] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠦ Processing command...
[17:24:51] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[17:24:51] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[17:24:51] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[17:24:52] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[17:24:52] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[17:24:52] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.33s
⠋ Processing command...
[17:24:52] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:52] [STDOUT] 
🧭 Navigating to section: ruang_publik
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🧭 Navigating to section: ruang_publik
[17:24:52] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[17:24:52] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:52] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[17:24:52] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[17:24:52] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[17:24:52] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠙ Processing command...
[17:24:52] [STDOUT] 
✓ Classified 21 elements instantly
⠙ Processing command...
[17:24:52] [STDOUT] 
✓ Headless discovery found 21 elements
⠙ Processing command...
[17:24:52] [STDOUT] 
✅ Headless discovery: 21 elements
⠙ Processing command...
[17:24:52] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[17:24:52] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[17:24:52] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠹ Processing command...
[17:24:52] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠹ Processing command...
[17:24:52] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[17:24:52] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[17:24:52] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[17:24:52] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠴ Processing command...
[17:24:52] [STDOUT] 
✅ Visual validation: additional elements found
⠴ Processing command...
[17:24:52] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.38s
⠴ Processing command...
[17:24:52] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠴ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠦ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:52] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[17:24:52] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:52] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[17:24:52] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:52] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[17:24:52] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[17:24:52] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[17:24:52] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[17:24:52] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠧ Processing command...
[17:24:52] [STDOUT] 
✓ Classified 21 elements instantly
⠧ Processing command...
[17:24:52] [STDOUT] 
✓ Headless discovery found 21 elements
⠧ Processing command...
[17:24:52] [STDOUT] 
✅ Headless discovery: 21 elements
⠧ Processing command...
[17:24:52] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[17:24:52] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[17:24:52] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠇ Processing command...
[17:24:52] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠇ Processing command...
[17:24:52] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[17:24:52] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[17:24:52] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[17:24:53] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[17:24:53] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[17:24:53] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.55s
⠹ Processing command...
[17:24:53] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠹ Processing command...
[17:24:53] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:53] [STDOUT] 
🧭 Navigating to section: ruang_orang_tua
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🧭 Navigating to section: ruang_orang_tua
[17:24:53] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[17:24:53] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:53] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[17:24:53] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[17:24:53] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[17:24:53] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠼ Processing command...
[17:24:53] [STDOUT] 
✓ Classified 21 elements instantly
⠼ Processing command...
[17:24:53] [STDOUT] 
✓ Headless discovery found 21 elements
⠼ Processing command...
[17:24:53] [STDOUT] 
✅ Headless discovery: 21 elements
⠼ Processing command...
[17:24:53] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[17:24:53] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[17:24:53] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠴ Processing command...
[17:24:53] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠴ Processing command...
[17:24:53] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[17:24:53] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[17:24:53] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[17:24:53] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠇ Processing command...
[17:24:53] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[17:24:53] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.42s
⠇ Processing command...
[17:24:53] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:53] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:53] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[17:24:53] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[17:24:53] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[17:24:53] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[17:24:53] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 12 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 42 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 42 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 72 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 90 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:53] [STDOUT] 
🚀 Found 120 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[17:24:53] [STDOUT] 
✓ Parsed 21 unique elements from XML
⠏ Processing command...
[17:24:53] [STDOUT] 
✓ Classified 21 elements instantly
⠏ Processing command...
[17:24:53] [STDOUT] 
✓ Headless discovery found 21 elements
⠏ Processing command...
[17:24:53] [STDOUT] 
✅ Headless discovery: 21 elements
⠏ Processing command...
[17:24:53] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[17:24:53] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[17:24:53] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠋ Processing command...
[17:24:53] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠋ Processing command...
[17:24:53] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[17:24:53] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[17:24:53] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[17:24:54] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠸ Processing command...
[17:24:54] [STDOUT] 
✅ Visual validation: additional elements found
⠸ Processing command...
[17:24:54] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 21 total elements in 0.40s
⠸ Processing command...
[17:24:54] [STDOUT] 
✅ Hybrid Headless Detection found 21 elements
⠸ Processing command...
[17:24:54] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 21 elements
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[17:24:54] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:54] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[17:24:54] [STDOUT] 
📊 TARGETED ANALYSIS COMPLETE: 0 additional elements found
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 📊 TARGETED ANALYSIS COMPLETE: 0 additional elements found
[17:24:54] [STDOUT] 
✅ Targeted analysis found 0 additional elements
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] ✅ Targeted analysis found 0 additional elements
[17:24:54] [STDOUT] 
📊 FEATURE ANALYSIS COMPLETION VERIFICATION:
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 📊 FEATURE ANALYSIS COMPLETION VERIFICATION:
[17:24:54] [STDOUT] 
   Features analyzed: 0/5 (0.0%)
⠼ Processing command...
[17:24:54] [RICH_CONSOLE]    Features analyzed: 0/5 (0.0%)
[17:24:54] [STDOUT] 
🎯 100% COMPLETION REQUIREMENT: All features must be analyzed
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 100% COMPLETION REQUIREMENT: All features must be analyzed
[17:24:54] [STDOUT] 
🔍 MANDATORY STRUCTURE CHECK: Verifying element coverage...
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🔍 MANDATORY STRUCTURE CHECK: Verifying element coverage...
[17:24:54] [STDOUT] 
🔍 Verifying mandatory structure elements coverage...
⠼ Processing command...
[17:24:54] [RICH_CONSOLE] 🔍 Verifying mandatory structure elements coverage...
[17:24:54] [STDOUT] 
📊 Structure Coverage: 100.0%
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] 📊 Structure Coverage: 100.0%
[17:24:54] [STDOUT] 
📊 Missing Elements: 0
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] 📊 Missing Elements: 0
[17:24:54] [STDOUT] 
✅ 100% STRUCTURE COVERAGE ACHIEVED!
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] ✅ 100% STRUCTURE COVERAGE ACHIEVED!
[17:24:54] [STDOUT] 
✅ 100% Structure coverage achieved!
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] ✅ 100% Structure coverage achieved!
[17:24:54] [STDOUT] 
🎯 With complete structure coverage, allowing flexible feature completion threshold: 80.0%
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] 🎯 With complete structure coverage, allowing flexible feature completion threshold: 80.0%
[17:24:54] [STDOUT] 
❌ Feature analysis critically low: 0.0% < 15.0%
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] ❌ Feature analysis critically low: 0.0% < 15.0%
[17:24:54] [STDOUT] 
⚠️ Missing 5 features - analysis should continue
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] ⚠️ Missing 5 features - analysis should continue
[17:24:54] [STDOUT] 
❌ Analysis remains incomplete even after targeted missing elements analysis
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] ❌ Analysis remains incomplete even after targeted missing elements analysis
[17:24:54] [STDOUT] 
🔍 Verifying mandatory structure elements coverage...
⠹ Processing command...
[17:24:54] [RICH_CONSOLE] 🔍 Verifying mandatory structure elements coverage...
[17:24:55] [STDOUT] 
📊 Structure Coverage: 100.0%
⠙ Processing command...
[17:24:55] [RICH_CONSOLE] 📊 Structure Coverage: 100.0%
[17:24:55] [STDOUT] 
📊 Missing Elements: 0
⠙ Processing command...
[17:24:55] [RICH_CONSOLE] 📊 Missing Elements: 0
[17:24:55] [STDOUT] 
✅ 100% STRUCTURE COVERAGE ACHIEVED!
⠙ Processing command...
[17:24:55] [RICH_CONSOLE] ✅ 100% STRUCTURE COVERAGE ACHIEVED!
[17:24:55] [ERROR] ❌ ERROR in Deep UI Analysis: Analysis incomplete - insufficient feature coverage (0.0% < 80.0% required with 100% structure coverage)
[17:24:55] [ERROR] ❌ Error Type: Exception
[17:24:55] [ERROR] ❌ Stack Trace:
[17:24:55] [ERROR]     NoneType: None
[17:24:55] [ERROR] ❌ Additional Context:
[17:24:55] [ERROR]     error_message: Analysis incomplete - insufficient feature coverage (0.0% < 80.0% required with 100% structure coverage)
[17:24:55] [ERROR]     analysis_duration: 496.94s
[17:24:55] [ERROR]     elements_found_before_failure: 0
[17:24:55] [ERROR]     step: deep_ui_analysis
[17:24:55] [INFO] 📱 AI Analysis - ui_analysis_failed: UI analysis failed: Analysis incomplete - insufficient feature coverage (0.0% < 80.0% required with 100% structure coverage)
[17:24:55] [INFO] ============================================================
[17:24:55] [ERROR] ❌ AI Analysis Failed: Analysis incomplete - insufficient feature coverage (0.0% < 80.0% required with 100% structure coverage)
[17:24:55] [INFO] End Time: 2025-06-20 17:24:55

================================================================================
Session End Time: 2025-06-20 17:24:55
Log File: logs/ai_analysis_20250620_171433_terminal.txt
================================================================================
