
================================================================================
sMTm Framework - Terminal Log Session
================================================================================
Session ID: ai_analysis_20250620_220821
Start Time: 2025-06-20 22:08:21
Command: /ai-analysis
================================================================================

[22:08:21] [INFO] 🚀 AI Analysis Started for app: Unknown
[22:08:21] [INFO] ============================================================
[22:08:21] [INFO] 📱 AI Analysis - session_start: AI Analysis session started: ai_analysis_20250620_220821
[22:08:21] [INFO] 📋 === AI ANALYSIS SESSION ===
[22:08:21] [INFO]   session_id: ai_analysis_20250620_220821
[22:08:21] [INFO]   start_time: 2025-06-20T22:08:21.738267
[22:08:21] [INFO]   custom_instructions: comprehensive
[22:08:21] [INFO]   analysis_mode: comprehensive_ui_analysis
[22:08:21] [INFO] 📋 === END AI ANALYSIS SESSION ===
[22:08:21] [STDOUT] Detected OS: macOS
[22:08:21] [RICH_CONSOLE] Detected OS: macOS
[22:08:21] [INFO] 📱 AI Analysis - os_detection: Detected OS: macOS
[22:08:21] [INFO] 📋 === OS DETECTION ===
[22:08:21] [INFO]   detected_os: macOS
[22:08:21] [INFO]   detection_method: OSDetector
[22:08:21] [INFO]   timestamp: 2025-06-20T22:08:21.739438
[22:08:21] [INFO] 📋 === END OS DETECTION ===
[22:08:21] [STDOUT] Use existing installation? (y/n):
[22:08:21] [RICH_CONSOLE] Use existing installation? (y/n):
[22:08:27] [INFO] 📱 AI Analysis - installation_preference: Use existing installation: True
[22:08:27] [STDOUT] What mobile OS would you like to analyze?
[22:08:27] [RICH_CONSOLE] What mobile OS would you like to analyze?
[22:08:27] [STDOUT] 1. Android
[22:08:27] [RICH_CONSOLE] 1. Android
[22:08:27] [STDOUT] 2. iOS
[22:08:27] [RICH_CONSOLE] 2. iOS
[22:08:27] [STDOUT] Enter your choice:
[22:08:27] [RICH_CONSOLE] Enter your choice:
[22:08:29] [INFO] 📱 AI Analysis - mobile_os_selection: Selected mobile OS: android
[22:08:29] [STDOUT] 
[22:08:29] [INFO] 📱 AI Analysis - android_setup_start: Starting Android environment setup
[22:08:29] [STDOUT] Checking Android environment...
⠋ Processing command...
[22:08:29] [RICH_CONSOLE] Checking Android environment...
[22:08:29] [STDOUT] 
Android emulator is already running
⠋ Processing command...
[22:08:29] [RICH_CONSOLE] Android emulator is already running
[22:08:29] [STDOUT] 
Connecting to Android device...
⠋ Processing command...
[22:08:29] [RICH_CONSOLE] Connecting to Android device...
[22:08:29] [STDOUT] Connecting to Android device...
[22:08:29] [STDOUT] 🔍 Validating device readiness: emulator-5554
[22:08:29] [STDOUT]   📡 Testing basic shell connectivity (attempt 1/3, timeout: 8s)...
[22:08:29] [STDOUT]   ✅ Shell connectivity OK (attempt 1)
[22:08:29] [STDOUT]   🚀 Checking boot completion status...
[22:08:29] [STATUS] Processing command...
[22:08:29] [STDOUT]   ✅ Device fully booted
[22:08:29] [STDOUT]   📱 Testing device responsiveness...
[22:08:29] [STDOUT]   ✅ Device responsive (Android 14)
[22:08:29] [STDOUT] 🎉 Device emulator-5554 is ready for connection!
[22:08:29] [STDOUT] ✅ 1 device(s) fully ready
[22:08:29] [STDOUT] 📱 Found 1 device(s)
[22:08:29] [STDOUT]   Device 1: emulator-5554 (status: device)
[22:08:29] [STDOUT] 🔗 Attempting to connect to device: emulator-5554
[22:08:29] [STDOUT] 🔧 Preparing device for connection...
[22:08:31] [STDOUT] 🔄 Trying direct connection strategy (timeout: 20s)...
[22:08:31] [STDOUT]   🔌 Attempting uiautomator2 connection...
[22:08:32] [STDOUT]   🧪 Verifying connection...
[22:08:32] [STDOUT] ✓ Device connection established using direct strategy
[22:08:32] [STDOUT]   📱 Device: sdk_gphone64_arm64
[22:08:32] [STDOUT] 
✓ Android Hardware Button Controller initialized
⠏ Processing command...
[22:08:32] [RICH_CONSOLE] ✓ Android Hardware Button Controller initialized
[22:08:32] [STDOUT] 
✓ Android environment setup completed!
⠏ Processing command...
[22:08:32] [RICH_CONSOLE] ✓ Android environment setup completed!
[22:08:32] [INFO] 📋 === ANDROID SETUP SUCCESS ===
[22:08:32] [INFO]   setup_duration: 3.18s
[22:08:32] [INFO]   device_connected: False
[22:08:32] [INFO]   emulator_status: unknown
[22:08:32] [INFO]   android_version: unknown
[22:08:32] [INFO] 📋 === END ANDROID SETUP SUCCESS ===
[22:08:32] [INFO] 📱 AI Analysis - android_setup_success: Android environment setup completed
[22:08:32] [INFO] 📱 AI Analysis - app_selection_start: Starting app selection and preparation
[22:08:32] [STDOUT] 📱 Analyzing available applications...
[22:08:32] [RICH_CONSOLE] 📱 Analyzing available applications...
[22:08:32] [STDOUT] 📋 Available applications:
[22:08:32] [RICH_CONSOLE] 📋 Available applications:
[22:08:32] [STDOUT]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[22:08:32] [RICH_CONSOLE]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[22:08:32] [STDOUT]   2. Rumah Pendidikan (APK File)
[22:08:32] [RICH_CONSOLE]   2. Rumah Pendidikan (APK File)
[22:08:32] [STDOUT] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[22:08:32] [RICH_CONSOLE] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[22:08:32] [STDOUT] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[22:08:32] [RICH_CONSOLE] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[22:08:32] [STDOUT] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[22:08:32] [RICH_CONSOLE] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[22:08:32] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[22:08:32] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[22:08:32] [STDOUT] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [STDOUT] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [STDOUT] 🚀 Skipping launch - proceeding directly to analysis
[22:08:32] [RICH_CONSOLE] 🚀 Skipping launch - proceeding directly to analysis
[22:08:32] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[22:08:32] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[22:08:32] [INFO] 📱 AI Analysis - app_selected: Selected app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [INFO] 📱 AI Analysis - custom_instructions: Custom analysis: comprehensive
[22:08:32] [STDOUT] 🎯 Custom Analysis Instructions: comprehensive
[22:08:32] [RICH_CONSOLE] 🎯 Custom Analysis Instructions: comprehensive
[22:08:32] [INFO] 🚀 AI Analysis Started for app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [INFO] ============================================================
[22:08:32] [STDOUT] 
[22:08:32] [INFO] 📱 AI Analysis - ui_analysis_start: Starting deep UI analysis
[22:08:32] [INFO] 📋 === UI ANALYSIS CONFIGURATION ===
[22:08:32] [INFO]   custom_instructions: comprehensive
[22:08:32] [INFO]   target_elements: 3000
[22:08:32] [INFO]   analysis_mode: comprehensive
[22:08:32] [INFO]   app_package: com.kemendikdasmen.rumahpendidikan
[22:08:32] [INFO] 📋 === END UI ANALYSIS CONFIGURATION ===
[22:08:32] [STDOUT] 🔍 Starting sequential, organized UI analysis...
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] 🔍 Starting sequential, organized UI analysis...
[22:08:32] [STDOUT] 
🔧 Initializing AI improvement system...
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] 🔧 Initializing AI improvement system...
[22:08:32] [STDOUT] 
✅ Professional Hybrid Headless Detector initialized
⠋ Processing command...
[22:08:32] [STDOUT] 
✅ Enhanced Element Classifier with Mobile Locator Knowledge initialized
⠋ Processing command...
[22:08:32] [STDOUT] 
✅ Real-time Problem Solver initialized
⠋ Processing command...
[22:08:32] [STDOUT] 
✅ AI Self-Improvement System initialized successfully
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] ✅ AI Self-Improvement System initialized successfully
[22:08:32] [STDOUT] 
🔧 AI improvement system initialization completed
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] 🔧 AI improvement system initialization completed
[22:08:32] [STDOUT] 
✅ AI improvement system initialized successfully
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] ✅ AI improvement system initialized successfully
[22:08:32] [STDOUT] 
🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] 🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
[22:08:32] [STDOUT] 
✅ collect_all_elements_robustly method exists on analyzer
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists on analyzer
[22:08:32] [STDOUT] 
🔍 Method type: <class 'method'>
⠋ Processing command...
[22:08:32] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[22:08:32] [STDOUT] 
📱 Current app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[22:08:32] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [STDOUT] 
🎯 Target app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[22:08:32] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[22:08:32] [STDOUT] 
✅ Verified: Analysis starting in correct main app
⠙ Processing command...
[22:08:32] [RICH_CONSOLE] ✅ Verified: Analysis starting in correct main app
[22:08:32] [STDOUT] 
🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
⠙ Processing command...
[22:08:32] [RICH_CONSOLE] 🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
[22:08:32] [STDOUT] 
✅ No corrupted Excel files found
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] ✅ No corrupted Excel files found
[22:08:32] [INFO] 📋 === LOOP PREVENTION SYSTEM RESET ===
[22:08:32] [INFO]   emergency_exit_triggered: False
[22:08:32] [INFO]   analysis_start_time: 2025-06-20T22:08:32.927301
[22:08:32] [INFO]   timeout_minutes: 45
[22:08:32] [INFO]   consecutive_failures_threshold: 8
[22:08:32] [INFO]   reset_timestamp: 2025-06-20T22:08:32.927304
[22:08:32] [INFO] 📋 === END LOOP PREVENTION SYSTEM RESET ===
[22:08:32] [STDOUT] 
✅ Loop prevention system reset for new analysis session
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] ✅ Loop prevention system reset for new analysis session
[22:08:32] [STDOUT] 
🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
[22:08:32] [STDOUT] 
🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
[22:08:32] [STDOUT] 
🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
[22:08:32] [STDOUT] 
📋 AI will autonomously discover and analyze entire application hierarchy
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 📋 AI will autonomously discover and analyze entire application hierarchy
[22:08:32] [STDOUT] 
🎯 Target: Complete menu structure, sub-menus, and all navigation elements
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 🎯 Target: Complete menu structure, sub-menus, and all navigation elements
[22:08:32] [STDOUT] 
🚀 ENHANCED: 3000+ element collection with 60-minute duration
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 🚀 ENHANCED: 3000+ element collection with 60-minute duration
[22:08:32] [STDOUT] 
📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
[22:08:32] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE TARGET:
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE TARGET:
[22:08:32] [STDOUT] 
   • Beranda with all subsections (1.1-1.15)
⠹ Processing command...
[22:08:32] [RICH_CONSOLE]    • Beranda with all subsections (1.1-1.15)
[22:08:32] [STDOUT] 
   • Ruang GTK with all sub-elements (1.4.1-1.4.19)
⠹ Processing command...
[22:08:32] [RICH_CONSOLE]    • Ruang GTK with all sub-elements (1.4.1-1.4.19)
[22:08:32] [STDOUT] 
   • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
⠹ Processing command...
[22:08:32] [RICH_CONSOLE]    • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
[22:08:32] [STDOUT] 
   • Complete navigation bar elements
⠹ Processing command...
[22:08:32] [RICH_CONSOLE]    • Complete navigation bar elements
[22:08:32] [STDOUT] 
   • External app first-page elements only
⠹ Processing command...
[22:08:32] [RICH_CONSOLE]    • External app first-page elements only
[22:08:32] [STDOUT] 
⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
⠹ Processing command...
[22:08:32] [RICH_CONSOLE] ⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
[22:08:33] [STDOUT] 
🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[22:08:33] [RICH_CONSOLE] 🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
[22:08:33] [STDOUT] 
🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
⠸ Processing command...
[22:08:33] [RICH_CONSOLE] 🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
[22:08:33] [STDOUT] 
🎯 Main app context set: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[22:08:33] [STDOUT] 
🧠 Intelligent navigation system initialized
⠸ Processing command...
[22:08:33] [RICH_CONSOLE] 🧠 Intelligent navigation system initialized
[22:08:33] [STDOUT] 
📂 Loading existing elements from persistent file: rumahpendidikan_persistent_analysis.xlsx
⠸ Processing command...
[22:08:33] [STDOUT] 
✅ Loaded 394 existing elements from persistent file
⠦ Processing command...
[22:08:33] [STDOUT] 
✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] ✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
[22:08:33] [STDOUT] 
✅ Persistent Excel file is valid: rumahpendidikan_persistent_analysis.xlsx
⠦ Processing command...
[22:08:33] [STDOUT] 
📋 Using persistent Excel storage only - no separate live save files created
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 📋 Using persistent Excel storage only - no separate live save files created
[22:08:33] [STDOUT] 
🎯 Performance Goals Tracking Started:
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 🎯 Performance Goals Tracking Started:
[22:08:33] [STDOUT] 
  • Target Elements: 5000
⠦ Processing command...
[22:08:33] [RICH_CONSOLE]   • Target Elements: 5000
[22:08:33] [STDOUT] 
  • Target Duration: 90 minutes
⠦ Processing command...
[22:08:33] [RICH_CONSOLE]   • Target Duration: 90 minutes
[22:08:33] [STDOUT] 
  • Max Unknown Elements: 2.0%
⠦ Processing command...
[22:08:33] [RICH_CONSOLE]   • Max Unknown Elements: 2.0%
[22:08:33] [STDOUT] 
🔍 Real-time problem monitoring started in background
⠦ Processing command...
[22:08:33] [STDOUT] 
🔧 Real-time problem solver monitoring started
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 🔧 Real-time problem solver monitoring started
[22:08:33] [STDOUT] 
🔍 Searching for previous analysis state for rumahpendidikan...
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 🔍 Searching for previous analysis state for rumahpendidikan...
[22:08:33] [STDOUT] 
📋 Checking persistent Excel manager for analysis state...
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 📋 Checking persistent Excel manager for analysis state...
[22:08:33] [STDOUT] 
⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] ⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
[22:08:33] [STDOUT] 
📂 Falling back to search for legacy live analysis files...
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 📂 Falling back to search for legacy live analysis files...
[22:08:33] [STDOUT] 
📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
[22:08:33] [STDOUT] 
⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] ⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
[22:08:33] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
[22:08:33] [STDOUT] 
📋 Target: Complete Rumah Pendidikan UI hierarchy collection
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 📋 Target: Complete Rumah Pendidikan UI hierarchy collection
[22:08:33] [STDOUT] 
📋 Phase 1: Comprehensive main page analysis and feature identification...
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 📋 Phase 1: Comprehensive main page analysis and feature identification...
[22:08:33] [STDOUT] 
🔍 Collecting all elements from main page...
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 🔍 Collecting all elements from main page...
[22:08:33] [STDOUT] 
✅ collect_all_elements_robustly method exists
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists
[22:08:33] [STDOUT] 
🔍 Method type: <class 'method'>
⠦ Processing command...
[22:08:33] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[22:08:33] [STDOUT] 🛡️ ROBUST ELEMENT COLLECTION STARTED
[22:08:33] [STDOUT] 📋 Strategy: Checkpoint-based progressive collection with crash recovery
[22:08:33] [STDOUT] 📋 Initializing robust collection state...
[22:08:33] [STDOUT] 📊 Found 394 existing elements in persistent storage
[22:08:33] [STDOUT] ✅ Collection state initialized
[22:08:33] [STDOUT] 🎯 Starting progressive collection with checkpoints...
[22:08:33] [STDOUT] 📋 SECTION 1/9: Beranda
[22:08:33] [STDOUT] 📍 Checkpoint created for section: Beranda
[22:08:33] [STDOUT] 🔍 Collecting elements from section: Beranda
[22:08:33] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:33] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:33] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:33] [STDOUT] 📊 Selector 1/13: //*
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 395)
⠙ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] 
✅ NEW element added: 10:08  (Total in memory: 396)
⠙ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 10:08 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 397)
⠙ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 398)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 399)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 400)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 401)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 402)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 403)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 404)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 405)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 406)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 407)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 408)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 409)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 410)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 411)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 412)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 413)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] 
✅ NEW element added:  (Total in memory: 414)
⠹ Processing command...
[22:08:33] [STDOUT] ✅ NEW element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:33] [STDOUT] 📊 Selector 2/13: //*
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:33] [STDOUT] 📊 Selector 3/13: //*
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ⏭️ EXISTING element: 
[22:08:33] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:33] [STDOUT] 📊 Selector 4/13: //*
[22:08:34] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:34] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:34] [STDOUT] 📊 Selector 5/13: //*
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:34] [STDOUT] 📊 Selector 6/13: //*
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:34] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:34] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:34] [STDOUT] 📊 Selector 9/13: //*
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:34] [STDOUT] 📊 Selector 10/13: //*
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:34] [STDOUT] 📊 Selector 11/13: //*
[22:08:34] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:34] [STDOUT] 📊 Selector 12/13: //*
[22:08:34] [STDOUT] ⏭️ EXISTING element: 
[22:08:34] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:34] [STDOUT] 📊 Selector 13/13: //*
[22:08:34] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:34] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:34] [STDOUT]   • //*: 63 total, 44 unique
[22:08:34] [STDOUT]   • //*: 14 total, 0 unique
[22:08:34] [STDOUT]   • //*: 62 total, 0 unique
[22:08:34] [STDOUT]   • //*: 1 total, 0 unique
[22:08:34] [STDOUT]   • //*: 62 total, 0 unique
[22:08:34] [STDOUT]   • //*: 62 total, 0 unique
[22:08:34] [STDOUT]   • //*: 20 total, 0 unique
[22:08:34] [STDOUT]   • //*: 1 total, 0 unique
[22:08:34] [STDOUT]   • //*: 0 total, 0 unique
[22:08:34] [STDOUT]   • //*: 1 total, 0 unique
[22:08:34] [STDOUT]   • //*: 0 total, 0 unique
[22:08:34] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:34] [STDOUT] ✅ Section 'Beranda' completed: 44 elements
[22:08:34] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠼ Processing command...
[22:08:34] [STDOUT] 
📊 Elements in memory: 414
⠼ Processing command...
[22:08:34] [STDOUT] 
📊 Elements list prepared: 414
⠼ Processing command...
[22:08:34] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠼ Processing command...
[22:08:34] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠼ Processing command...
[22:08:34] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠼ Processing command...
[22:08:34] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠴ Processing command...
[22:08:34] [STDOUT] 
📊 Creating Statistics sheet...
⠴ Processing command...
[22:08:34] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠴ Processing command...
[22:08:34] [STDOUT] 
📊 Creating Metadata sheet...
⠴ Processing command...
[22:08:34] [STDOUT] 
✅ Metadata sheet created
⠴ Processing command...
[22:08:34] [STDOUT] 
✅ Persistent Excel file saved successfully
⠦ Processing command...
[22:08:34] [STDOUT] 
📊 Total elements in memory: 414
⠦ Processing command...
[22:08:34] [STDOUT] 
📊 Total elements saved: 414
⠦ Processing command...
[22:08:34] [STDOUT] 
🆕 New elements: 20
⠦ Processing command...
[22:08:34] [STDOUT] 
🔄 Updated elements: 0
⠦ Processing command...
[22:08:34] [STDOUT] 
⏭️ Skipped elements: 266
⠦ Processing command...
[22:08:34] [STDOUT] 💾 Progress saved: 44 elements, 1 sections
[22:08:34] [STDOUT] 📋 SECTION 2/9: Ruang GTK
[22:08:35] [STDOUT] 📍 Checkpoint created for section: Ruang GTK
[22:08:35] [STDOUT] 🔍 Collecting elements from section: Ruang GTK
[22:08:35] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:35] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:35] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:35] [STDOUT] 📊 Selector 1/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:35] [STDOUT] 📊 Selector 2/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 3/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 4/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:35] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 5/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 6/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:35] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:35] [STDOUT] 📊 Selector 9/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 10/13: //*
[22:08:35] [STDOUT] ⏭️ EXISTING element: 
[22:08:35] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:35] [STDOUT] 📊 Selector 11/13: //*
[22:08:36] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:36] [STDOUT] 📊 Selector 12/13: //*
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:36] [STDOUT] 📊 Selector 13/13: //*
[22:08:36] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:36] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:36] [STDOUT]   • //*: 63 total, 44 unique
[22:08:36] [STDOUT]   • //*: 14 total, 0 unique
[22:08:36] [STDOUT]   • //*: 62 total, 0 unique
[22:08:36] [STDOUT]   • //*: 1 total, 0 unique
[22:08:36] [STDOUT]   • //*: 62 total, 0 unique
[22:08:36] [STDOUT]   • //*: 62 total, 0 unique
[22:08:36] [STDOUT]   • //*: 20 total, 0 unique
[22:08:36] [STDOUT]   • //*: 1 total, 0 unique
[22:08:36] [STDOUT]   • //*: 0 total, 0 unique
[22:08:36] [STDOUT]   • //*: 1 total, 0 unique
[22:08:36] [STDOUT]   • //*: 0 total, 0 unique
[22:08:36] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:36] [STDOUT] ✅ Section 'Ruang GTK' completed: 44 elements
[22:08:36] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠹ Processing command...
[22:08:36] [STDOUT] 
📊 Elements in memory: 414
⠹ Processing command...
[22:08:36] [STDOUT] 
📊 Elements list prepared: 414
⠸ Processing command...
[22:08:36] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠸ Processing command...
[22:08:36] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠸ Processing command...
[22:08:36] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠸ Processing command...
[22:08:36] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠼ Processing command...
[22:08:36] [STDOUT] 
📊 Creating Statistics sheet...
⠼ Processing command...
[22:08:36] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠼ Processing command...
[22:08:36] [STDOUT] 
📊 Creating Metadata sheet...
⠼ Processing command...
[22:08:36] [STDOUT] 
✅ Metadata sheet created
⠼ Processing command...
[22:08:36] [STDOUT] 
✅ Persistent Excel file saved successfully
⠴ Processing command...
[22:08:36] [STDOUT] 
📊 Total elements in memory: 414
⠴ Processing command...
[22:08:36] [STDOUT] 
📊 Total elements saved: 414
⠴ Processing command...
[22:08:36] [STDOUT] 
🆕 New elements: 20
⠴ Processing command...
[22:08:36] [STDOUT] 
🔄 Updated elements: 0
⠴ Processing command...
[22:08:36] [STDOUT] 
⏭️ Skipped elements: 552
⠴ Processing command...
[22:08:36] [STDOUT] 💾 Progress saved: 88 elements, 2 sections
[22:08:36] [STDOUT] 📋 SECTION 3/9: Ruang Murid
[22:08:36] [STDOUT] 📍 Checkpoint created for section: Ruang Murid
[22:08:36] [STDOUT] 🔍 Collecting elements from section: Ruang Murid
[22:08:36] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:36] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:36] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:36] [STDOUT] 📊 Selector 1/13: //*
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:36] [STDOUT] 📊 Selector 2/13: //*
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:36] [STDOUT] 📊 Selector 3/13: //*
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ⏭️ EXISTING element: 
[22:08:36] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:36] [STDOUT] 📊 Selector 4/13: //*
[22:08:37] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:37] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:37] [STDOUT] 📊 Selector 5/13: //*
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:37] [STDOUT] 📊 Selector 6/13: //*
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:37] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:37] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:37] [STDOUT] 📊 Selector 9/13: //*
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:37] [STDOUT] 📊 Selector 10/13: //*
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:37] [STDOUT] 📊 Selector 11/13: //*
[22:08:37] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:37] [STDOUT] 📊 Selector 12/13: //*
[22:08:37] [STDOUT] ⏭️ EXISTING element: 
[22:08:37] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:37] [STDOUT] 📊 Selector 13/13: //*
[22:08:37] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:37] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:37] [STDOUT]   • //*: 63 total, 44 unique
[22:08:37] [STDOUT]   • //*: 14 total, 0 unique
[22:08:37] [STDOUT]   • //*: 62 total, 0 unique
[22:08:37] [STDOUT]   • //*: 1 total, 0 unique
[22:08:37] [STDOUT]   • //*: 62 total, 0 unique
[22:08:37] [STDOUT]   • //*: 62 total, 0 unique
[22:08:37] [STDOUT]   • //*: 20 total, 0 unique
[22:08:37] [STDOUT]   • //*: 1 total, 0 unique
[22:08:37] [STDOUT]   • //*: 0 total, 0 unique
[22:08:37] [STDOUT]   • //*: 1 total, 0 unique
[22:08:37] [STDOUT]   • //*: 0 total, 0 unique
[22:08:37] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:37] [STDOUT] ✅ Section 'Ruang Murid' completed: 44 elements
[22:08:37] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠹ Processing command...
[22:08:37] [STDOUT] 
📊 Elements in memory: 414
⠹ Processing command...
[22:08:37] [STDOUT] 
📊 Elements list prepared: 414
⠹ Processing command...
[22:08:37] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠹ Processing command...
[22:08:37] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠹ Processing command...
[22:08:37] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠹ Processing command...
[22:08:37] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠸ Processing command...
[22:08:37] [STDOUT] 
📊 Creating Statistics sheet...
⠸ Processing command...
[22:08:37] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠸ Processing command...
[22:08:37] [STDOUT] 
📊 Creating Metadata sheet...
⠸ Processing command...
[22:08:37] [STDOUT] 
✅ Metadata sheet created
⠸ Processing command...
[22:08:37] [STDOUT] 
✅ Persistent Excel file saved successfully
⠼ Processing command...
[22:08:37] [STDOUT] 
📊 Total elements in memory: 414
⠼ Processing command...
[22:08:37] [STDOUT] 
📊 Total elements saved: 414
⠼ Processing command...
[22:08:37] [STDOUT] 
🆕 New elements: 20
⠼ Processing command...
[22:08:37] [STDOUT] 
🔄 Updated elements: 0
⠼ Processing command...
[22:08:37] [STDOUT] 
⏭️ Skipped elements: 838
⠼ Processing command...
[22:08:37] [STDOUT] 💾 Progress saved: 132 elements, 3 sections
[22:08:37] [STDOUT] 📋 SECTION 4/9: Ruang sekolah
[22:08:38] [STDOUT] 📍 Checkpoint created for section: Ruang sekolah
[22:08:38] [STDOUT] 🔍 Collecting elements from section: Ruang sekolah
[22:08:38] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:38] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:38] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:38] [STDOUT] 📊 Selector 1/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:38] [STDOUT] 📊 Selector 2/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:38] [STDOUT] 📊 Selector 3/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:38] [STDOUT] 📊 Selector 4/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:38] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:38] [STDOUT] 📊 Selector 5/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:38] [STDOUT] 📊 Selector 6/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:38] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:38] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:38] [STDOUT] 📊 Selector 9/13: //*
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ⏭️ EXISTING element: 
[22:08:38] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:38] [STDOUT] 📊 Selector 10/13: //*
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:39] [STDOUT] 📊 Selector 11/13: //*
[22:08:39] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:39] [STDOUT] 📊 Selector 12/13: //*
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:39] [STDOUT] 📊 Selector 13/13: //*
[22:08:39] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:39] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:39] [STDOUT]   • //*: 63 total, 44 unique
[22:08:39] [STDOUT]   • //*: 14 total, 0 unique
[22:08:39] [STDOUT]   • //*: 62 total, 0 unique
[22:08:39] [STDOUT]   • //*: 1 total, 0 unique
[22:08:39] [STDOUT]   • //*: 62 total, 0 unique
[22:08:39] [STDOUT]   • //*: 62 total, 0 unique
[22:08:39] [STDOUT]   • //*: 20 total, 0 unique
[22:08:39] [STDOUT]   • //*: 1 total, 0 unique
[22:08:39] [STDOUT]   • //*: 0 total, 0 unique
[22:08:39] [STDOUT]   • //*: 1 total, 0 unique
[22:08:39] [STDOUT]   • //*: 0 total, 0 unique
[22:08:39] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:39] [STDOUT] ✅ Section 'Ruang sekolah' completed: 44 elements
[22:08:39] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠙ Processing command...
[22:08:39] [STDOUT] 
📊 Elements in memory: 414
⠙ Processing command...
[22:08:39] [STDOUT] 
📊 Elements list prepared: 414
⠙ Processing command...
[22:08:39] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠙ Processing command...
[22:08:39] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠙ Processing command...
[22:08:39] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠹ Processing command...
[22:08:39] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠸ Processing command...
[22:08:39] [STDOUT] 
📊 Creating Statistics sheet...
⠸ Processing command...
[22:08:39] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠸ Processing command...
[22:08:39] [STDOUT] 
📊 Creating Metadata sheet...
⠸ Processing command...
[22:08:39] [STDOUT] 
✅ Metadata sheet created
⠸ Processing command...
[22:08:39] [STDOUT] 
✅ Persistent Excel file saved successfully
⠼ Processing command...
[22:08:39] [STDOUT] 
📊 Total elements in memory: 414
⠼ Processing command...
[22:08:39] [STDOUT] 
📊 Total elements saved: 414
⠼ Processing command...
[22:08:39] [STDOUT] 
🆕 New elements: 20
⠼ Processing command...
[22:08:39] [STDOUT] 
🔄 Updated elements: 0
⠼ Processing command...
[22:08:39] [STDOUT] 
⏭️ Skipped elements: 1124
⠼ Processing command...
[22:08:39] [STDOUT] 💾 Progress saved: 176 elements, 4 sections
[22:08:39] [STDOUT] 📋 SECTION 5/9: Ruang bahasa
[22:08:39] [STDOUT] 📍 Checkpoint created for section: Ruang bahasa
[22:08:39] [STDOUT] 🔍 Collecting elements from section: Ruang bahasa
[22:08:39] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:39] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:39] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:39] [STDOUT] 📊 Selector 1/13: //*
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:39] [STDOUT] 📊 Selector 2/13: //*
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ⏭️ EXISTING element: 
[22:08:39] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:39] [STDOUT] 📊 Selector 3/13: //*
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:40] [STDOUT] 📊 Selector 4/13: //*
[22:08:40] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:40] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:40] [STDOUT] 📊 Selector 5/13: //*
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:40] [STDOUT] 📊 Selector 6/13: //*
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ⏭️ EXISTING element: 
[22:08:40] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:40] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:40] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:40] [STDOUT] 📊 Selector 9/13: //*
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:41] [STDOUT] 📊 Selector 10/13: //*
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:41] [STDOUT] 📊 Selector 11/13: //*
[22:08:41] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:41] [STDOUT] 📊 Selector 12/13: //*
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:41] [STDOUT] 📊 Selector 13/13: //*
[22:08:41] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:41] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:41] [STDOUT]   • //*: 63 total, 44 unique
[22:08:41] [STDOUT]   • //*: 14 total, 0 unique
[22:08:41] [STDOUT]   • //*: 62 total, 0 unique
[22:08:41] [STDOUT]   • //*: 1 total, 0 unique
[22:08:41] [STDOUT]   • //*: 62 total, 0 unique
[22:08:41] [STDOUT]   • //*: 62 total, 0 unique
[22:08:41] [STDOUT]   • //*: 20 total, 0 unique
[22:08:41] [STDOUT]   • //*: 1 total, 0 unique
[22:08:41] [STDOUT]   • //*: 0 total, 0 unique
[22:08:41] [STDOUT]   • //*: 1 total, 0 unique
[22:08:41] [STDOUT]   • //*: 0 total, 0 unique
[22:08:41] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:41] [STDOUT] ✅ Section 'Ruang bahasa' completed: 44 elements
[22:08:41] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠇ Processing command...
[22:08:41] [STDOUT] 
📊 Elements in memory: 414
⠇ Processing command...
[22:08:41] [STDOUT] 
📊 Elements list prepared: 414
⠇ Processing command...
[22:08:41] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠇ Processing command...
[22:08:41] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠇ Processing command...
[22:08:41] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠇ Processing command...
[22:08:41] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠏ Processing command...
[22:08:41] [STDOUT] 
📊 Creating Statistics sheet...
⠏ Processing command...
[22:08:41] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠏ Processing command...
[22:08:41] [STDOUT] 
📊 Creating Metadata sheet...
⠏ Processing command...
[22:08:41] [STDOUT] 
✅ Metadata sheet created
⠏ Processing command...
[22:08:41] [STDOUT] 
✅ Persistent Excel file saved successfully
⠙ Processing command...
[22:08:41] [STDOUT] 
📊 Total elements in memory: 414
⠙ Processing command...
[22:08:41] [STDOUT] 
📊 Total elements saved: 414
⠙ Processing command...
[22:08:41] [STDOUT] 
🆕 New elements: 20
⠙ Processing command...
[22:08:41] [STDOUT] 
🔄 Updated elements: 0
⠙ Processing command...
[22:08:41] [STDOUT] 
⏭️ Skipped elements: 1410
⠙ Processing command...
[22:08:41] [STDOUT] 💾 Progress saved: 220 elements, 5 sections
[22:08:41] [STDOUT] 📋 SECTION 6/9: Ruang pemerintah
[22:08:41] [STDOUT] 📍 Checkpoint created for section: Ruang pemerintah
[22:08:41] [STDOUT] 🔍 Collecting elements from section: Ruang pemerintah
[22:08:41] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:41] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:41] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:41] [STDOUT] 📊 Selector 1/13: //*
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ⏭️ EXISTING element: 
[22:08:41] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:41] [STDOUT] 📊 Selector 2/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 3/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 4/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:42] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 5/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 6/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:42] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:42] [STDOUT] 📊 Selector 9/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 10/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 11/13: //*
[22:08:42] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:42] [STDOUT] 📊 Selector 12/13: //*
[22:08:42] [STDOUT] ⏭️ EXISTING element: 
[22:08:42] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:42] [STDOUT] 📊 Selector 13/13: //*
[22:08:42] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:42] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:42] [STDOUT]   • //*: 63 total, 44 unique
[22:08:42] [STDOUT]   • //*: 14 total, 0 unique
[22:08:42] [STDOUT]   • //*: 62 total, 0 unique
[22:08:42] [STDOUT]   • //*: 1 total, 0 unique
[22:08:42] [STDOUT]   • //*: 62 total, 0 unique
[22:08:42] [STDOUT]   • //*: 62 total, 0 unique
[22:08:42] [STDOUT]   • //*: 20 total, 0 unique
[22:08:42] [STDOUT]   • //*: 1 total, 0 unique
[22:08:42] [STDOUT]   • //*: 0 total, 0 unique
[22:08:42] [STDOUT]   • //*: 1 total, 0 unique
[22:08:42] [STDOUT]   • //*: 0 total, 0 unique
[22:08:42] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:42] [STDOUT] ✅ Section 'Ruang pemerintah' completed: 44 elements
[22:08:42] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠧ Processing command...
[22:08:42] [STDOUT] 
📊 Elements in memory: 414
⠧ Processing command...
[22:08:42] [STDOUT] 
📊 Elements list prepared: 414
⠧ Processing command...
[22:08:42] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠧ Processing command...
[22:08:42] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠧ Processing command...
[22:08:43] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠧ Processing command...
[22:08:43] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠏ Processing command...
[22:08:43] [STDOUT] 
📊 Creating Statistics sheet...
⠏ Processing command...
[22:08:43] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠏ Processing command...
[22:08:43] [STDOUT] 
📊 Creating Metadata sheet...
⠏ Processing command...
[22:08:43] [STDOUT] 
✅ Metadata sheet created
⠏ Processing command...
[22:08:43] [STDOUT] 
✅ Persistent Excel file saved successfully
⠋ Processing command...
[22:08:43] [STDOUT] 
📊 Total elements in memory: 414
⠋ Processing command...
[22:08:43] [STDOUT] 
📊 Total elements saved: 414
⠋ Processing command...
[22:08:43] [STDOUT] 
🆕 New elements: 20
⠋ Processing command...
[22:08:43] [STDOUT] 
🔄 Updated elements: 0
⠋ Processing command...
[22:08:43] [STDOUT] 
⏭️ Skipped elements: 1696
⠋ Processing command...
[22:08:43] [STDOUT] 💾 Progress saved: 264 elements, 6 sections
[22:08:43] [STDOUT] 📋 SECTION 7/9: Ruang mitra
[22:08:43] [STDOUT] 📍 Checkpoint created for section: Ruang mitra
[22:08:43] [STDOUT] 🔍 Collecting elements from section: Ruang mitra
[22:08:43] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:43] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:43] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:43] [STDOUT] 📊 Selector 1/13: //*
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:43] [STDOUT] 📊 Selector 2/13: //*
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:43] [STDOUT] 📊 Selector 3/13: //*
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:43] [STDOUT] 📊 Selector 4/13: //*
[22:08:43] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:43] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:43] [STDOUT] 📊 Selector 5/13: //*
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ⏭️ EXISTING element: 
[22:08:43] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:43] [STDOUT] 📊 Selector 6/13: //*
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:44] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:44] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:44] [STDOUT] 📊 Selector 9/13: //*
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:44] [STDOUT] 📊 Selector 10/13: //*
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:44] [STDOUT] 📊 Selector 11/13: //*
[22:08:44] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:44] [STDOUT] 📊 Selector 12/13: //*
[22:08:44] [STDOUT] ⏭️ EXISTING element: 
[22:08:44] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:44] [STDOUT] 📊 Selector 13/13: //*
[22:08:44] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:44] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:44] [STDOUT]   • //*: 63 total, 44 unique
[22:08:44] [STDOUT]   • //*: 14 total, 0 unique
[22:08:44] [STDOUT]   • //*: 62 total, 0 unique
[22:08:44] [STDOUT]   • //*: 1 total, 0 unique
[22:08:44] [STDOUT]   • //*: 62 total, 0 unique
[22:08:44] [STDOUT]   • //*: 62 total, 0 unique
[22:08:44] [STDOUT]   • //*: 20 total, 0 unique
[22:08:44] [STDOUT]   • //*: 1 total, 0 unique
[22:08:44] [STDOUT]   • //*: 0 total, 0 unique
[22:08:44] [STDOUT]   • //*: 1 total, 0 unique
[22:08:44] [STDOUT]   • //*: 0 total, 0 unique
[22:08:44] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:44] [STDOUT] ✅ Section 'Ruang mitra' completed: 44 elements
[22:08:44] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠧ Processing command...
[22:08:44] [STDOUT] 
📊 Elements in memory: 414
⠧ Processing command...
[22:08:44] [STDOUT] 
📊 Elements list prepared: 414
⠧ Processing command...
[22:08:44] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠧ Processing command...
[22:08:44] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠧ Processing command...
[22:08:44] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠇ Processing command...
[22:08:44] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠏ Processing command...
[22:08:44] [STDOUT] 
📊 Creating Statistics sheet...
⠏ Processing command...
[22:08:44] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠏ Processing command...
[22:08:44] [STDOUT] 
📊 Creating Metadata sheet...
⠏ Processing command...
[22:08:44] [STDOUT] 
✅ Metadata sheet created
⠏ Processing command...
[22:08:44] [STDOUT] 
✅ Persistent Excel file saved successfully
⠋ Processing command...
[22:08:44] [STDOUT] 
📊 Total elements in memory: 414
⠋ Processing command...
[22:08:44] [STDOUT] 
📊 Total elements saved: 414
⠋ Processing command...
[22:08:44] [STDOUT] 
🆕 New elements: 20
⠋ Processing command...
[22:08:44] [STDOUT] 
🔄 Updated elements: 0
⠋ Processing command...
[22:08:44] [STDOUT] 
⏭️ Skipped elements: 1982
⠋ Processing command...
[22:08:44] [STDOUT] 💾 Progress saved: 308 elements, 7 sections
[22:08:44] [STDOUT] 📋 SECTION 8/9: Ruang Publik
[22:08:44] [STDOUT] 📍 Checkpoint created for section: Ruang Publik
[22:08:44] [STDOUT] 🔍 Collecting elements from section: Ruang Publik
[22:08:44] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:44] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:44] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:44] [STDOUT] 📊 Selector 1/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:45] [STDOUT] 📊 Selector 2/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 3/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 4/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:45] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 5/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 6/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:45] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:45] [STDOUT] 📊 Selector 9/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 10/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 11/13: //*
[22:08:45] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:45] [STDOUT] 📊 Selector 12/13: //*
[22:08:45] [STDOUT] ⏭️ EXISTING element: 
[22:08:45] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:45] [STDOUT] 📊 Selector 13/13: //*
[22:08:45] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:45] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:45] [STDOUT]   • //*: 63 total, 44 unique
[22:08:45] [STDOUT]   • //*: 14 total, 0 unique
[22:08:45] [STDOUT]   • //*: 62 total, 0 unique
[22:08:45] [STDOUT]   • //*: 1 total, 0 unique
[22:08:45] [STDOUT]   • //*: 62 total, 0 unique
[22:08:45] [STDOUT]   • //*: 62 total, 0 unique
[22:08:45] [STDOUT]   • //*: 20 total, 0 unique
[22:08:45] [STDOUT]   • //*: 1 total, 0 unique
[22:08:45] [STDOUT]   • //*: 0 total, 0 unique
[22:08:45] [STDOUT]   • //*: 1 total, 0 unique
[22:08:45] [STDOUT]   • //*: 0 total, 0 unique
[22:08:45] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:45] [STDOUT] ✅ Section 'Ruang Publik' completed: 44 elements
[22:08:45] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠴ Processing command...
[22:08:45] [STDOUT] 
📊 Elements in memory: 414
⠴ Processing command...
[22:08:45] [STDOUT] 
📊 Elements list prepared: 414
⠴ Processing command...
[22:08:45] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠴ Processing command...
[22:08:45] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠴ Processing command...
[22:08:46] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠴ Processing command...
[22:08:46] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠧ Processing command...
[22:08:46] [STDOUT] 
📊 Creating Statistics sheet...
⠧ Processing command...
[22:08:46] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠧ Processing command...
[22:08:46] [STDOUT] 
📊 Creating Metadata sheet...
⠧ Processing command...
[22:08:46] [STDOUT] 
✅ Metadata sheet created
⠧ Processing command...
[22:08:46] [STDOUT] 
✅ Persistent Excel file saved successfully
⠇ Processing command...
[22:08:46] [STDOUT] 
📊 Total elements in memory: 414
⠇ Processing command...
[22:08:46] [STDOUT] 
📊 Total elements saved: 414
⠇ Processing command...
[22:08:46] [STDOUT] 
🆕 New elements: 20
⠇ Processing command...
[22:08:46] [STDOUT] 
🔄 Updated elements: 0
⠇ Processing command...
[22:08:46] [STDOUT] 
⏭️ Skipped elements: 2268
⠇ Processing command...
[22:08:46] [STDOUT] 💾 Progress saved: 352 elements, 8 sections
[22:08:46] [STDOUT] 📋 SECTION 9/9: Ruang Orang Tua
[22:08:46] [STDOUT] 📍 Checkpoint created for section: Ruang Orang Tua
[22:08:46] [STDOUT] 🔍 Collecting elements from section: Ruang Orang Tua
[22:08:46] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:46] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:46] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:46] [STDOUT] 📊 Selector 1/13: //*
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:46] [STDOUT] 📊 Selector 2/13: //*
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:46] [STDOUT] 📊 Selector 3/13: //*
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:46] [STDOUT] 📊 Selector 4/13: //*
[22:08:46] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:46] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:46] [STDOUT] 📊 Selector 5/13: //*
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ⏭️ EXISTING element: 
[22:08:46] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:46] [STDOUT] 📊 Selector 6/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:47] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:47] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:47] [STDOUT] 📊 Selector 9/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:47] [STDOUT] 📊 Selector 10/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:47] [STDOUT] 📊 Selector 11/13: //*
[22:08:47] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:47] [STDOUT] 📊 Selector 12/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:47] [STDOUT] 📊 Selector 13/13: //*
[22:08:47] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:47] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:47] [STDOUT]   • //*: 63 total, 44 unique
[22:08:47] [STDOUT]   • //*: 14 total, 0 unique
[22:08:47] [STDOUT]   • //*: 62 total, 0 unique
[22:08:47] [STDOUT]   • //*: 1 total, 0 unique
[22:08:47] [STDOUT]   • //*: 62 total, 0 unique
[22:08:47] [STDOUT]   • //*: 62 total, 0 unique
[22:08:47] [STDOUT]   • //*: 20 total, 0 unique
[22:08:47] [STDOUT]   • //*: 1 total, 0 unique
[22:08:47] [STDOUT]   • //*: 0 total, 0 unique
[22:08:47] [STDOUT]   • //*: 1 total, 0 unique
[22:08:47] [STDOUT]   • //*: 0 total, 0 unique
[22:08:47] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:47] [STDOUT] ✅ Section 'Ruang Orang Tua' completed: 44 elements
[22:08:47] [STDOUT] 
💾 Saving to persistent Excel file: rumahpendidikan_persistent_analysis.xlsx
⠼ Processing command...
[22:08:47] [STDOUT] 
📊 Elements in memory: 414
⠼ Processing command...
[22:08:47] [STDOUT] 
📊 Elements list prepared: 414
⠼ Processing command...
[22:08:47] [STDOUT] 
📊 DataFrame created with 414 rows and 61 columns
⠼ Processing command...
[22:08:47] [STDOUT] 
📊 Creating Overview sheet (Summary + Statistics)...
⠼ Processing command...
[22:08:47] [STDOUT] 
✅ Overview sheet created with 54 metrics
⠼ Processing command...
[22:08:47] [STDOUT] 
✅ Elements sheet saved with 414 rows
⠴ Processing command...
[22:08:47] [STDOUT] 
📊 Creating Statistics sheet...
⠴ Processing command...
[22:08:47] [STDOUT] 
✅ Statistics sheet created with 18 metrics
⠴ Processing command...
[22:08:47] [STDOUT] 
📊 Creating Metadata sheet...
⠴ Processing command...
[22:08:47] [STDOUT] 
✅ Metadata sheet created
⠴ Processing command...
[22:08:47] [STDOUT] 
✅ Persistent Excel file saved successfully
⠦ Processing command...
[22:08:47] [STDOUT] 
📊 Total elements in memory: 414
⠦ Processing command...
[22:08:47] [STDOUT] 
📊 Total elements saved: 414
⠦ Processing command...
[22:08:47] [STDOUT] 
🆕 New elements: 20
⠦ Processing command...
[22:08:47] [STDOUT] 
🔄 Updated elements: 0
⠦ Processing command...
[22:08:47] [STDOUT] 
⏭️ Skipped elements: 2554
⠦ Processing command...
[22:08:47] [STDOUT] 💾 Progress saved: 396 elements, 9 sections
[22:08:47] [STDOUT] 🧭 Collecting navigation elements...
[22:08:47] [STDOUT] 🔍 Collecting elements from section: Navigation
[22:08:47] [STDOUT] 📊 Strategy 1/4: _strategy_screenshot_based_collection
[22:08:47] [STDOUT] 📸 Using comprehensive screenshot-based collection strategy
[22:08:47] [STDOUT] 🎯 COMPREHENSIVE COLLECTION: Using 13 selectors for maximum coverage
[22:08:47] [STDOUT] 📊 Selector 1/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 63 elements (44 new unique)
[22:08:47] [STDOUT] 📊 Selector 2/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 14 elements (0 new unique)
[22:08:47] [STDOUT] 📊 Selector 3/13: //*
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ⏭️ EXISTING element: 
[22:08:47] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:47] [STDOUT] 📊 Selector 4/13: //*
[22:08:48] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:48] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:48] [STDOUT] 📊 Selector 5/13: //*
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:48] [STDOUT] 📊 Selector 6/13: //*
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 10:08 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ✅ Selector '//*' found 62 elements (0 new unique)
[22:08:48] [STDOUT] 📊 Selector 7/13: //android.widget.*
[22:08:48] [STDOUT] 📊 Selector 8/13: //android.view.*
[22:08:48] [STDOUT] 📊 Selector 9/13: //*
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ✅ Selector '//*' found 20 elements (0 new unique)
[22:08:48] [STDOUT] 📊 Selector 10/13: //*
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:48] [STDOUT] 📊 Selector 11/13: //*
[22:08:48] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:48] [STDOUT] 📊 Selector 12/13: //*
[22:08:48] [STDOUT] ⏭️ EXISTING element: 
[22:08:48] [STDOUT] ✅ Selector '//*' found 1 elements (0 new unique)
[22:08:48] [STDOUT] 📊 Selector 13/13: //*
[22:08:48] [STDOUT] ⚠️ Selector '//*' found 0 elements
[22:08:48] [STDOUT] 🎯 COMPREHENSIVE COLLECTION COMPLETE: 44 unique elements found
[22:08:48] [STDOUT]   • //*: 63 total, 44 unique
[22:08:48] [STDOUT]   • //*: 14 total, 0 unique
[22:08:48] [STDOUT]   • //*: 62 total, 0 unique
[22:08:48] [STDOUT]   • //*: 1 total, 0 unique
[22:08:48] [STDOUT]   • //*: 62 total, 0 unique
[22:08:48] [STDOUT]   • //*: 62 total, 0 unique
[22:08:48] [STDOUT]   • //*: 20 total, 0 unique
[22:08:48] [STDOUT]   • //*: 1 total, 0 unique
[22:08:48] [STDOUT]   • //*: 0 total, 0 unique
[22:08:48] [STDOUT]   • //*: 1 total, 0 unique
[22:08:48] [STDOUT]   • //*: 0 total, 0 unique
[22:08:48] [STDOUT] ✅ Strategy successful: 44 elements collected
[22:08:48] [STDOUT] ✅ Navigation collection: 44 elements
[22:08:48] [STDOUT] 🔍 Verifying collection completeness...
[22:08:48] [STDOUT] ✅ Collection verification complete: 100.0% sections, Quality: 10.0/10
[22:08:48] [STDOUT] ✅ ROBUST COLLECTION COMPLETED: 440 elements
[22:08:48] [INFO] ⏱️ === PERFORMANCE METRICS ===
[22:08:48] [INFO]   total_elements: 414
[22:08:48] [INFO] ⏱️ === END PERFORMANCE METRICS ===
[22:08:48] [INFO] ⏱️ === PERFORMANCE METRICS ===
[22:08:48] [INFO]   analyzed_elements: 414
[22:08:48] [INFO] ⏱️ === END PERFORMANCE METRICS ===
[22:08:48] [INFO] 📱 [22:08:48.722] Device Action - element_collection: Successfully collected 414 elements
[22:08:48] [STDOUT] 
✅ Found 414 elements on main page
⠏ Processing command...
[22:08:48] [RICH_CONSOLE] ✅ Found 414 elements on main page
[22:08:48] [STDOUT] 
🔍 Analyzing main page with 414 elements to identify features...
⠏ Processing command...
[22:08:48] [RICH_CONSOLE] 🔍 Analyzing main page with 414 elements to identify features...
[22:08:48] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠏ Processing command...
[22:08:48] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:08:49] [STDOUT] 
✅ Successfully identified 10 main features
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] ✅ Successfully identified 10 main features
[22:08:49] [STDOUT] 
✅ Identified 10 main features for comprehensive analysis
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] ✅ Identified 10 main features for comprehensive analysis
[22:08:49] [STDOUT] 
  Feature 1: Ruang GTK (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 1: Ruang GTK (unknown)
[22:08:49] [STDOUT] 
  Feature 2: Ruang Murid (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 2: Ruang Murid (unknown)
[22:08:49] [STDOUT] 
  Feature 3: Ruang Sekolah (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 3: Ruang Sekolah (unknown)
[22:08:49] [STDOUT] 
  Feature 4: Ruang Bahasa (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 4: Ruang Bahasa (unknown)
[22:08:49] [STDOUT] 
  Feature 5: Ruang Pemerintah (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 5: Ruang Pemerintah (unknown)
[22:08:49] [STDOUT] 
  Feature 6: Ruang Mitra (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 6: Ruang Mitra (unknown)
[22:08:49] [STDOUT] 
  Feature 7: Ruang Publik (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 7: Ruang Publik (unknown)
[22:08:49] [STDOUT] 
  Feature 8: Ruang Orang Tua (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 8: Ruang Orang Tua (unknown)
[22:08:49] [STDOUT] 
  Feature 9: Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 9: Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang (unknown)
[22:08:49] [STDOUT] 
  Feature 10: Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat (unknown)
⠸ Processing command...
[22:08:49] [RICH_CONSOLE]   Feature 10: Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat (unknown)
[22:08:49] [STDOUT] 
📋 Phase 2: Comprehensive feature analysis (10 features)...
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 
📋 Phase 2: Comprehensive feature analysis (10 features)...
[22:08:49] [STDOUT] 
🎯 COMPREHENSIVE: AI will analyze each feature exhaustively for 3000+ elements
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 🎯 COMPREHENSIVE: AI will analyze each feature exhaustively for 3000+ elements
[22:08:49] [STDOUT] 
🎯 === ANALYZING FEATURE 1/10: 'Ruang GTK' (unknown) ===
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 1/10: 'Ruang GTK' (unknown) ===
[22:08:49] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Ruang GTK' for maximum element discovery
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Ruang GTK' for maximum element discovery
[22:08:49] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[22:08:49] [STDOUT] 
🏥 Pre-feature health check for 'Ruang GTK'...
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Ruang GTK'...
[22:08:49] [STDOUT] 
🏥 Checking application health...
⠸ Processing command...
[22:08:49] [RICH_CONSOLE] 🏥 Checking application health...
[22:08:49] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[22:08:49] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:08:49] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[22:08:49] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[22:08:49] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[22:08:49] [STDOUT] 
🚀 Found 14 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[22:08:49] [STDOUT] 
🚀 Found 48 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[22:08:49] [STDOUT] 
🚀 Found 48 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[22:08:49] [STDOUT] 
🚀 Found 82 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[22:08:49] [STDOUT] 
🚀 Found 101 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[22:08:49] [STDOUT] 
🚀 Found 135 elements using fallback strategy (unlimited mode - continuing)
⠇ Processing command...
[22:08:49] [STDOUT] 
✓ Parsed 22 unique elements from XML
⠇ Processing command...
[22:08:49] [STDOUT] 
✓ Classified 22 elements instantly
⠇ Processing command...
[22:08:49] [STDOUT] 
✓ Headless discovery found 22 elements
⠇ Processing command...
[22:08:49] [STDOUT] 
✅ Headless discovery: 22 elements
⠇ Processing command...
[22:08:49] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠇ Processing command...
[22:08:49] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠇ Processing command...
[22:08:49] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠇ Processing command...
[22:08:49] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠇ Processing command...
[22:08:49] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[22:08:49] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[22:08:49] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[22:08:49] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[22:08:49] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[22:08:49] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 22 total elements in 0.51s
⠹ Processing command...
[22:08:49] [STDOUT] 
✅ Hybrid Headless Detection found 22 elements
⠹ Processing command...
[22:08:49] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 22 elements
[22:08:49] [STDOUT] 
✅ UI elements accessible: 22 elements found
⠼ Processing command...
[22:08:49] [RICH_CONSOLE] ✅ UI elements accessible: 22 elements found
[22:08:50] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[22:08:50] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:08:50] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[22:08:50] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[22:08:50] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[22:08:50] [STDOUT] 
🚀 Found 14 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:08:50] [STDOUT] 
🚀 Found 48 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:08:50] [STDOUT] 
🚀 Found 48 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:08:50] [STDOUT] 
🚀 Found 82 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:08:50] [STDOUT] 
🚀 Found 101 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[22:08:50] [STDOUT] 
🚀 Found 135 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[22:08:50] [STDOUT] 
✓ Parsed 22 unique elements from XML
⠧ Processing command...
[22:08:50] [STDOUT] 
✓ Classified 22 elements instantly
⠧ Processing command...
[22:08:50] [STDOUT] 
✓ Headless discovery found 22 elements
⠧ Processing command...
[22:08:50] [STDOUT] 
✅ Headless discovery: 22 elements
⠧ Processing command...
[22:08:50] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[22:08:50] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[22:08:50] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠧ Processing command...
[22:08:50] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠧ Processing command...
[22:08:50] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[22:08:50] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[22:08:50] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[22:08:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠙ Processing command...
[22:08:50] [STDOUT] 
✅ Visual validation: additional elements found
⠙ Processing command...
[22:08:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 22 total elements in 0.48s
⠙ Processing command...
[22:08:50] [STDOUT] 
✅ Hybrid Headless Detection found 22 elements
⠙ Processing command...
[22:08:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 22 elements
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:08:50] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:08:50] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:08:50] [STDOUT] 
✅ App is responsive
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] ✅ App is responsive
[22:08:50] [STDOUT] 
📊 Feature Ruang GTK started:
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 📊 Feature Ruang GTK started:
[22:08:50] [STDOUT] 
   Elements: 0
⠹ Processing command...
[22:08:50] [RICH_CONSOLE]    Elements: 0
[22:08:50] [STDOUT] 
   Interactions: 0
⠹ Processing command...
[22:08:50] [RICH_CONSOLE]    Interactions: 0
[22:08:50] [STDOUT] 
   Sub-features: 0
⠹ Processing command...
[22:08:50] [RICH_CONSOLE]    Sub-features: 0
[22:08:50] [STDOUT] 
🔧 Problem solver updated: 0 elements, 0 unknown, 0.3min
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🔧 Problem solver updated: 0 elements, 0 unknown, 0.3min
[22:08:50] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Ruang GTK'
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Ruang GTK'
[22:08:50] [STDOUT] 
🎯 ACTUALLY ENTERING Feature 1/10: 'Ruang GTK'
⠹ Processing command...
[22:08:50] [RICH_CONSOLE] 🎯 ACTUALLY ENTERING Feature 1/10: 'Ruang GTK'
[22:08:50] [STDOUT] 
📸 Pre-click screen captured for verification
⠼ Processing command...
[22:08:50] [RICH_CONSOLE] 📸 Pre-click screen captured for verification
[22:08:50] [STDOUT] 
📝 Tracked main app interaction: 
⠦ Processing command...
[22:08:50] [RICH_CONSOLE] 📝 Tracked main app interaction: 
[22:08:50] [STDOUT] 
🖱️ CLICKING on feature element: 'Ruang GTK'
⠦ Processing command...
[22:08:50] [RICH_CONSOLE] 🖱️ CLICKING on feature element: 'Ruang GTK'
[22:08:51] [STDOUT] 
⏳ Waiting for feature 'Ruang GTK' to load completely...
⠏ Processing command...
[22:08:51] [RICH_CONSOLE] ⏳ Waiting for feature 'Ruang GTK' to load completely...
[22:08:54] [STDOUT] 
✅ NAVIGATION SUCCESSFUL: Screen changed after clicking 'Ruang GTK'
⠏ Processing command...
[22:08:54] [RICH_CONSOLE] ✅ NAVIGATION SUCCESSFUL: Screen changed after clicking 'Ruang GTK'
[22:08:54] [STDOUT] 
🎯 NOW INSIDE FEATURE 'Ruang GTK' - Starting comprehensive content discovery
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 🎯 NOW INSIDE FEATURE 'Ruang GTK' - Starting comprehensive content discovery
[22:08:54] [STDOUT] 
🔍 Discovering ALL content within 'Ruang GTK' feature...
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 🔍 Discovering ALL content within 'Ruang GTK' feature...
[22:08:54] [STDOUT] 
📊 Strategy 1: Comprehensive content discovery in 'Ruang GTK'
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 📊 Strategy 1: Comprehensive content discovery in 'Ruang GTK'
[22:08:54] [STDOUT] 
🔍 Strategy 1: Enhanced discovery in 'Ruang GTK' for missing sub-elements
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 🔍 Strategy 1: Enhanced discovery in 'Ruang GTK' for missing sub-elements
[22:08:54] [STDOUT] 
📜 Strategy 1: Enhanced scrolling + text-based element discovery
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 📜 Strategy 1: Enhanced scrolling + text-based element discovery
[22:08:54] [STDOUT] 
🔍 Enhanced element detection for missing sub-elements...
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 🔍 Enhanced element detection for missing sub-elements...
[22:08:54] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠙ Processing command...
[22:08:54] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:08:54] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠙ Processing command...
[22:08:54] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠙ Processing command...
[22:08:54] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠙ Processing command...
[22:08:54] [STDOUT] 
🚀 Found 5 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[22:08:54] [STDOUT] 
🚀 Found 35 elements using interactive strategy (unlimited mode - continuing)
⠹ Processing command...
[22:08:54] [STDOUT] 
🚀 Found 35 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[22:08:54] [STDOUT] 
🚀 Found 65 elements using content strategy (unlimited mode - continuing)
⠹ Processing command...
[22:08:54] [STDOUT] 
🚀 Found 77 elements using widgets strategy (unlimited mode - continuing)
⠹ Processing command...
[22:08:54] [STDOUT] 
🚀 Found 107 elements using fallback strategy (unlimited mode - continuing)
⠹ Processing command...
[22:08:54] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠹ Processing command...
[22:08:54] [STDOUT] 
✓ Classified 15 elements instantly
⠹ Processing command...
[22:08:54] [STDOUT] 
✓ Headless discovery found 15 elements
⠹ Processing command...
[22:08:54] [STDOUT] 
✅ Headless discovery: 15 elements
⠹ Processing command...
[22:08:54] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠹ Processing command...
[22:08:54] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠹ Processing command...
[22:08:54] [STDOUT] 
✅ Deep hierarchy parsing found 30 elements
⠹ Processing command...
[22:08:54] [STDOUT] 
✅ Deep parsing: 30 additional elements
⠹ Processing command...
[22:08:54] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[22:08:54] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[22:08:54] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[22:08:54] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[22:08:54] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[22:08:54] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.45s
⠦ Processing command...
[22:08:54] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠦ Processing command...
[22:08:54] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[22:08:54] [STDOUT] 
✅ Standard detection: 15 elements
⠦ Processing command...
[22:08:54] [RICH_CONSOLE] ✅ Standard detection: 15 elements
[22:08:54] [STDOUT] 
📝 Discovering text-based elements for missing sub-elements...
⠧ Processing command...
[22:08:54] [RICH_CONSOLE] 📝 Discovering text-based elements for missing sub-elements...
[22:08:55] [STDOUT] 
✅ Found 1 elements with pattern 'diklat'
⠏ Processing command...
[22:08:55] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'diklat'
[22:08:55] [STDOUT] 
✅ Found 2 elements with pattern 'pelatihan'
⠴ Processing command...
[22:08:55] [RICH_CONSOLE] ✅ Found 2 elements with pattern 'pelatihan'
[22:08:55] [STDOUT] 
✅ Found 1 elements with pattern 'komunitas'
⠇ Processing command...
[22:08:55] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'komunitas'
[22:08:56] [STDOUT] 
✅ Found 1 elements with pattern 'kinerja'
⠼ Processing command...
[22:08:56] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'kinerja'
[22:08:57] [STDOUT] 
✅ Found 1 elements with pattern 'kompetensi'
⠙ Processing command...
[22:08:57] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'kompetensi'
[22:08:58] [STDOUT] 
✅ Found 1 elements with pattern 'ajar'
⠧ Processing command...
[22:08:58] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'ajar'
[22:08:58] [STDOUT] 
✅ Found 1 elements with pattern 'praktik'
⠧ Processing command...
[22:08:58] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'praktik'
[22:09:01] [STDOUT] 
✅ Found 1 elements with pattern 'belajar'
⠋ Processing command...
[22:09:01] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'belajar'
[22:09:04] [STDOUT] 
✅ Found 3 elements with pattern 'pendidikan'
⠹ Processing command...
[22:09:04] [RICH_CONSOLE] ✅ Found 3 elements with pattern 'pendidikan'
[22:09:12] [STDOUT] 
✅ Found 1 elements with pattern 'informasi'
⠦ Processing command...
[22:09:12] [RICH_CONSOLE] ✅ Found 1 elements with pattern 'informasi'
[22:09:15] [STDOUT] 
✅ Text-based detection: 13 elements
⠦ Processing command...
[22:09:15] [RICH_CONSOLE] ✅ Text-based detection: 13 elements
[22:09:15] [STDOUT] 
🌳 Discovering elements from XML hierarchy...
⠦ Processing command...
[22:09:15] [RICH_CONSOLE] 🌳 Discovering elements from XML hierarchy...
[22:09:15] [STDOUT] 
✅ XML hierarchy detection: 0 elements
⠧ Processing command...
[22:09:15] [RICH_CONSOLE] ✅ XML hierarchy detection: 0 elements
[22:09:15] [STDOUT] 
📜 Discovering elements with enhanced scrolling...
⠧ Processing command...
[22:09:15] [RICH_CONSOLE] 📜 Discovering elements with enhanced scrolling...
[22:09:15] [STDOUT] 
📜 Scrolling down with 2 attempts...
⠇ Processing command...
[22:09:15] [RICH_CONSOLE] 📜 Scrolling down with 2 attempts...
[22:09:17] [STDOUT] 
✅ Scroll down #1 revealed 3 new elements
⠙ Processing command...
[22:09:17] [RICH_CONSOLE] ✅ Scroll down #1 revealed 3 new elements
[22:09:19] [STDOUT] 
⚠️ Scroll down #2 revealed no new elements
⠴ Processing command...
[22:09:19] [RICH_CONSOLE] ⚠️ Scroll down #2 revealed no new elements
[22:09:20] [STDOUT] 
📜 Scrolling up with 1 attempts...
⠙ Processing command...
[22:09:20] [RICH_CONSOLE] 📜 Scrolling up with 1 attempts...
[22:09:21] [STDOUT] 
⚠️ Scroll up #1 revealed no new elements
⠴ Processing command...
[22:09:21] [RICH_CONSOLE] ⚠️ Scroll up #1 revealed no new elements
[22:09:22] [STDOUT] 
📜 Scrolling down with 2 attempts...
⠙ Processing command...
[22:09:22] [RICH_CONSOLE] 📜 Scrolling down with 2 attempts...
[22:09:24] [STDOUT] 
⚠️ Scroll down #1 revealed no new elements
⠼ Processing command...
[22:09:24] [RICH_CONSOLE] ⚠️ Scroll down #1 revealed no new elements
[22:09:26] [STDOUT] 
⚠️ Scroll down #2 revealed no new elements
⠇ Processing command...
[22:09:26] [RICH_CONSOLE] ⚠️ Scroll down #2 revealed no new elements
[22:09:26] [STDOUT] 
📜 Scrolling horizontal with 1 attempts...
⠴ Processing command...
[22:09:26] [RICH_CONSOLE] 📜 Scrolling horizontal with 1 attempts...
[22:09:28] [STDOUT] 
⚠️ Scroll horizontal #1 revealed no new elements
⠇ Processing command...
[22:09:28] [RICH_CONSOLE] ⚠️ Scroll horizontal #1 revealed no new elements
[22:09:29] [STDOUT] 
✅ Scrolling complete: 3 elements found in 13.3s
⠼ Processing command...
[22:09:29] [RICH_CONSOLE] ✅ Scrolling complete: 3 elements found in 13.3s
[22:09:29] [STDOUT] 
✅ Scrolling detection: 3 elements
⠴ Processing command...
[22:09:29] [RICH_CONSOLE] ✅ Scrolling detection: 3 elements
[22:09:29] [STDOUT] 
🎯 Discovering sub-elements using specific patterns...
⠴ Processing command...
[22:09:29] [RICH_CONSOLE] 🎯 Discovering sub-elements using specific patterns...
[22:09:29] [STDOUT] 
🔍 Searching for ruang_gtk sub-elements...
⠴ Processing command...
[22:09:29] [RICH_CONSOLE] 🔍 Searching for ruang_gtk sub-elements...
[22:09:30] [STDOUT] 
✅ Found 1 content-desc elements for pattern 'pengelolaan kinerja'
⠹ Processing command...
[22:09:30] [RICH_CONSOLE] ✅ Found 1 content-desc elements for pattern 'pengelolaan kinerja'
[22:09:33] [STDOUT] 
✅ Found 2 content-desc elements for pattern 'asesmen'
⠴ Processing command...
[22:09:33] [RICH_CONSOLE] ✅ Found 2 content-desc elements for pattern 'asesmen'
[22:09:33] [STDOUT] 
✅ Found 2 content-desc elements for pattern 'kelas'
⠇ Processing command...
[22:09:33] [RICH_CONSOLE] ✅ Found 2 content-desc elements for pattern 'kelas'
[22:09:33] [STDOUT] 
🔍 Searching for ruang_murid sub-elements...
⠏ Processing command...
[22:09:33] [RICH_CONSOLE] 🔍 Searching for ruang_murid sub-elements...
[22:09:36] [STDOUT] 
🔍 Searching for ruang_sekolah sub-elements...
⠼ Processing command...
[22:09:36] [RICH_CONSOLE] 🔍 Searching for ruang_sekolah sub-elements...
[22:09:38] [STDOUT] 
🔍 Searching for ruang_bahasa sub-elements...
⠇ Processing command...
[22:09:38] [RICH_CONSOLE] 🔍 Searching for ruang_bahasa sub-elements...
[22:09:39] [STDOUT] 
🔍 Searching for ruang_pemerintah sub-elements...
⠴ Processing command...
[22:09:39] [RICH_CONSOLE] 🔍 Searching for ruang_pemerintah sub-elements...
[22:09:40] [STDOUT] 
🔍 Searching for ruang_mitra sub-elements...
⠋ Processing command...
[22:09:40] [RICH_CONSOLE] 🔍 Searching for ruang_mitra sub-elements...
[22:09:42] [STDOUT] 
🔍 Searching for ruang_publik sub-elements...
⠏ Processing command...
[22:09:42] [RICH_CONSOLE] 🔍 Searching for ruang_publik sub-elements...
[22:09:44] [STDOUT] 
🔍 Searching for ruang_orang_tua sub-elements...
⠦ Processing command...
[22:09:44] [RICH_CONSOLE] 🔍 Searching for ruang_orang_tua sub-elements...
[22:09:45] [STDOUT] 
✅ Pattern-based detection: 5 elements
⠋ Processing command...
[22:09:45] [RICH_CONSOLE] ✅ Pattern-based detection: 5 elements
[22:09:45] [STDOUT] 
✅ Enhanced detection complete: 16 unique elements found
⠋ Processing command...
[22:09:45] [RICH_CONSOLE] ✅ Enhanced detection complete: 16 unique elements found
[22:09:45] [STDOUT] 
✅ Found 16 elements in feature 'Ruang GTK' (enhanced detection)
⠋ Processing command...
[22:09:45] [RICH_CONSOLE] ✅ Found 16 elements in feature 'Ruang GTK' (enhanced detection)
[22:09:45] [STDOUT] 
🎯 Performing targeted search for missing sub-elements in 'Ruang GTK'...
⠋ Processing command...
[22:09:45] [RICH_CONSOLE] 🎯 Performing targeted search for missing sub-elements in 'Ruang GTK'...
[22:09:45] [STDOUT] 
🎯 Targeted search for missing elements in 'Ruang GTK'...
⠋ Processing command...
[22:09:45] [RICH_CONSOLE] 🎯 Targeted search for missing elements in 'Ruang GTK'...
[22:09:45] [STDOUT] 
🔍 Searching for 'diklat' in Ruang GTK...
⠋ Processing command...
[22:09:45] [RICH_CONSOLE] 🔍 Searching for 'diklat' in Ruang GTK...
[22:09:46] [STDOUT] 
🔍 Searching for 'sertifikat' in Ruang GTK...
⠦ Processing command...
[22:09:46] [RICH_CONSOLE] 🔍 Searching for 'sertifikat' in Ruang GTK...
[22:09:46] [STDOUT] 
🔍 Searching for 'pelatihan' in Ruang GTK...
⠙ Processing command...
[22:09:46] [RICH_CONSOLE] 🔍 Searching for 'pelatihan' in Ruang GTK...
[22:09:46] [STDOUT] 
🔍 Searching for 'komunitas' in Ruang GTK...
⠧ Processing command...
[22:09:46] [RICH_CONSOLE] 🔍 Searching for 'komunitas' in Ruang GTK...
[22:09:47] [STDOUT] 
🔍 Searching for 'pengelolaan' in Ruang GTK...
⠹ Processing command...
[22:09:47] [RICH_CONSOLE] 🔍 Searching for 'pengelolaan' in Ruang GTK...
[22:09:47] [STDOUT] 
✅ Found 3 content-desc matches for 'pengelolaan'
⠴ Processing command...
[22:09:47] [RICH_CONSOLE] ✅ Found 3 content-desc matches for 'pengelolaan'
[22:09:47] [STDOUT] 
🔍 Searching for 'kinerja' in Ruang GTK...
⠇ Processing command...
[22:09:47] [RICH_CONSOLE] 🔍 Searching for 'kinerja' in Ruang GTK...
[22:09:48] [STDOUT] 
✅ Found 1 content-desc matches for 'kinerja'
⠙ Processing command...
[22:09:48] [RICH_CONSOLE] ✅ Found 1 content-desc matches for 'kinerja'
[22:09:48] [STDOUT] 
🔍 Searching for 'seleksi' in Ruang GTK...
⠸ Processing command...
[22:09:48] [RICH_CONSOLE] 🔍 Searching for 'seleksi' in Ruang GTK...
[22:09:48] [STDOUT] 
🔍 Searching for 'kepala' in Ruang GTK...
⠇ Processing command...
[22:09:48] [RICH_CONSOLE] 🔍 Searching for 'kepala' in Ruang GTK...
[22:09:49] [STDOUT] 
🔍 Searching for 'refleksi' in Ruang GTK...
⠸ Processing command...
[22:09:49] [RICH_CONSOLE] 🔍 Searching for 'refleksi' in Ruang GTK...
[22:09:49] [STDOUT] 
🔍 Searching for 'kompetensi' in Ruang GTK...
⠏ Processing command...
[22:09:49] [RICH_CONSOLE] 🔍 Searching for 'kompetensi' in Ruang GTK...
[22:09:49] [STDOUT] 
✅ Found 1 content-desc matches for 'kompetensi'
⠹ Processing command...
[22:09:49] [RICH_CONSOLE] ✅ Found 1 content-desc matches for 'kompetensi'
[22:09:49] [STDOUT] 
🔍 Searching for 'perangkat' in Ruang GTK...
⠴ Processing command...
[22:09:49] [RICH_CONSOLE] 🔍 Searching for 'perangkat' in Ruang GTK...
[22:09:50] [STDOUT] 
🔍 Searching for 'ajar' in Ruang GTK...
⠋ Processing command...
[22:09:50] [RICH_CONSOLE] 🔍 Searching for 'ajar' in Ruang GTK...
[22:09:50] [STDOUT] 
✅ Found 2 content-desc matches for 'ajar'
⠸ Processing command...
[22:09:50] [RICH_CONSOLE] ✅ Found 2 content-desc matches for 'ajar'
[22:09:50] [STDOUT] 
🔍 Searching for 'cp' in Ruang GTK...
⠴ Processing command...
[22:09:50] [RICH_CONSOLE] 🔍 Searching for 'cp' in Ruang GTK...
[22:09:51] [STDOUT] 
🔍 Searching for 'atp' in Ruang GTK...
⠙ Processing command...
[22:09:51] [RICH_CONSOLE] 🔍 Searching for 'atp' in Ruang GTK...
[22:09:51] [STDOUT] 
🔍 Searching for 'ide' in Ruang GTK...
⠧ Processing command...
[22:09:51] [RICH_CONSOLE] 🔍 Searching for 'ide' in Ruang GTK...
[22:09:52] [STDOUT] 
✅ Found 5 resource-id matches for 'ide'
⠙ Processing command...
[22:09:52] [RICH_CONSOLE] ✅ Found 5 resource-id matches for 'ide'
[22:09:52] [STDOUT] 
🔍 Searching for 'praktik' in Ruang GTK...
⠹ Processing command...
[22:09:52] [RICH_CONSOLE] 🔍 Searching for 'praktik' in Ruang GTK...
[22:09:52] [STDOUT] 
🔍 Searching for 'bukti' in Ruang GTK...
⠇ Processing command...
[22:09:52] [RICH_CONSOLE] 🔍 Searching for 'bukti' in Ruang GTK...
[22:09:53] [STDOUT] 
🔍 Searching for 'karya' in Ruang GTK...
⠸ Processing command...
[22:09:53] [RICH_CONSOLE] 🔍 Searching for 'karya' in Ruang GTK...
[22:09:53] [STDOUT] 
🔍 Searching for 'video' in Ruang GTK...
⠏ Processing command...
[22:09:53] [RICH_CONSOLE] 🔍 Searching for 'video' in Ruang GTK...
[22:09:53] [STDOUT] 
🔍 Searching for 'inspirasi' in Ruang GTK...
⠴ Processing command...
[22:09:53] [RICH_CONSOLE] 🔍 Searching for 'inspirasi' in Ruang GTK...
[22:09:54] [STDOUT] 
✅ Found 1 content-desc matches for 'inspirasi'
⠇ Processing command...
[22:09:54] [RICH_CONSOLE] ✅ Found 1 content-desc matches for 'inspirasi'
[22:09:54] [STDOUT] 
🔍 Searching for 'asesmen' in Ruang GTK...
⠋ Processing command...
[22:09:54] [RICH_CONSOLE] 🔍 Searching for 'asesmen' in Ruang GTK...
[22:09:54] [STDOUT] 
✅ Found 2 content-desc matches for 'asesmen'
⠸ Processing command...
[22:09:54] [RICH_CONSOLE] ✅ Found 2 content-desc matches for 'asesmen'
[22:09:54] [STDOUT] 
🔍 Searching for 'kelas' in Ruang GTK...
⠴ Processing command...
[22:09:54] [RICH_CONSOLE] 🔍 Searching for 'kelas' in Ruang GTK...
[22:09:55] [STDOUT] 
✅ Found 2 content-desc matches for 'kelas'
⠏ Processing command...
[22:09:55] [RICH_CONSOLE] ✅ Found 2 content-desc matches for 'kelas'
[22:09:55] [STDOUT] 
🔍 Comprehensive clickable element search in Ruang GTK...
⠙ Processing command...
[22:09:55] [RICH_CONSOLE] 🔍 Comprehensive clickable element search in Ruang GTK...
[22:09:55] [STDOUT] 
✅ Targeted search complete: 17 missing elements found
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] ✅ Targeted search complete: 17 missing elements found
[22:09:55] [STDOUT] 
✅ Found 17 additional missing sub-elements
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] ✅ Found 17 additional missing sub-elements
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🔄 UPDATED element: 
⠹ Processing command...
[22:09:55] [STDOUT] 
🔄 UPDATED element in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:55] [STDOUT] 
🔄 UPDATED element: 
⠹ Processing command...
[22:09:55] [STDOUT] 
🔄 UPDATED element in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[22:09:55] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:09:55] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:09:55] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🔄 UPDATED element: 
⠹ Processing command...
[22:09:55] [STDOUT] 
🔄 UPDATED element in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
[22:09:55] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:55] [STDOUT] 
🔄 UPDATED element: 
⠹ Processing command...
[22:09:55] [STDOUT] 
🔄 UPDATED element in persistent storage
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:55] [STDOUT] 
📝 NEW Feature Element: 
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:09:55] [STDOUT] 
🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
[22:09:55] [STDOUT] 
🖱️ Exploring clickable content element
⠹ Processing command...
[22:09:55] [RICH_CONSOLE] 🖱️ Exploring clickable content element
[22:09:57] [STDOUT] 
✅ Content element opened new screen
⠹ Processing command...
[22:09:57] [RICH_CONSOLE] ✅ Content element opened new screen
[22:09:57] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[22:09:57] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:09:57] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[22:09:57] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[22:09:57] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[22:09:57] [STDOUT] 
🚀 Found 1 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:09:57] [STDOUT] 
🚀 Found 20 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:09:57] [STDOUT] 
🚀 Found 27 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:09:57] [STDOUT] 
🚀 Found 47 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:09:57] [STDOUT] 
🚀 Found 57 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[22:09:57] [STDOUT] 
🚀 Found 77 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[22:09:57] [STDOUT] 
✓ Parsed 14 unique elements from XML
⠸ Processing command...
[22:09:57] [STDOUT] 
✓ Classified 14 elements instantly
⠸ Processing command...
[22:09:57] [STDOUT] 
✓ Headless discovery found 14 elements
⠸ Processing command...
[22:09:57] [STDOUT] 
✅ Headless discovery: 14 elements
⠸ Processing command...
[22:09:57] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[22:09:57] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[22:09:57] [STDOUT] 
✅ Deep hierarchy parsing found 20 elements
⠸ Processing command...
[22:09:57] [STDOUT] 
✅ Deep parsing: 20 additional elements
⠸ Processing command...
[22:09:57] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[22:09:57] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[22:09:57] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[22:09:58] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠇ Processing command...
[22:09:58] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[22:09:58] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 14 total elements in 0.44s
⠇ Processing command...
[22:09:58] [STDOUT] 
✅ Hybrid Headless Detection found 14 elements
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 14 elements
[22:09:58] [STDOUT] 
✅ Found 14 elements on new screen
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] ✅ Found 14 elements on new screen
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:09:58] [STDOUT] 
🔄 UPDATED element: Webpage not available
⠇ Processing command...
[22:09:58] [STDOUT] 
🔄 UPDATED element in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element: Webpage not available
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element: Webpage not available
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🔄 UPDATED element: x+AAAAAElFTkSuQmCC
⠇ Processing command...
[22:09:58] [STDOUT] 
🔄 UPDATED element in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element: x+AAAAAElFTkSuQmCC
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element: x+AAAAAElFTkSuQmCC
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:09:58] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element: Webpage not available
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element: Webpage not available
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:09:58] [STDOUT] 
🔄 UPDATED element: The webpage at 
⠇ Processing command...
[22:09:58] [STDOUT] 
🔄 UPDATED element in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element: The webpage at 
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element: The webpage at 
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:09:58] [STDOUT] 
🔄 UPDATED element: https://guru.kemdikbud.go.id/d
⠇ Processing command...
[22:09:58] [STDOUT] 
🔄 UPDATED element in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element: https://guru.kemdikbud.go.id/dokumen/kategori/oWx0b8DZ1a
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element: https://guru.kemdikbud.go.id/dokumen/kategori/oWx0b8DZ1a
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:09:58] [STDOUT] 
🔄 UPDATED element:  could not be loaded because:
⠇ Processing command...
[22:09:58] [STDOUT] 
🔄 UPDATED element in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element:  could not be loaded because:
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element:  could not be loaded because:
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:09:58] [STDOUT] 
🔄 UPDATED element: net::ERR_INVALID_ARGUMENT
⠇ Processing command...
[22:09:58] [STDOUT] 
🔄 UPDATED element in persistent storage
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:09:58] [STDOUT] 
📝 NEW SubScreen Element: net::ERR_INVALID_ARGUMENT
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 📝 NEW SubScreen Element: net::ERR_INVALID_ARGUMENT
[22:09:58] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:09:58] [STDOUT] 
🔙 Returning to feature main screen
⠇ Processing command...
[22:09:58] [RICH_CONSOLE] 🔙 Returning to feature main screen
[22:10:00] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:00] [STDOUT] 
🔄 UPDATED element: 
⠼ Processing command...
[22:10:00] [STDOUT] 
🔄 UPDATED element in persistent storage
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:00] [STDOUT] 
📝 NEW Feature Element: 
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:00] [STDOUT] 
🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
[22:10:00] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:00] [STDOUT] 
🔄 UPDATED element: 
⠼ Processing command...
[22:10:00] [STDOUT] 
🔄 UPDATED element in persistent storage
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:00] [STDOUT] 
📝 NEW Feature Element: 
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:00] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:00] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:00] [STDOUT] 
🔄 UPDATED element: 
⠼ Processing command...
[22:10:00] [STDOUT] 
🔄 UPDATED element in persistent storage
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:00] [STDOUT] 
📝 NEW Feature Element: 
⠼ Processing command...
[22:10:00] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:00] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠴ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:00] [STDOUT] 
🔄 UPDATED element: 
⠴ Processing command...
[22:10:00] [STDOUT] 
🔄 UPDATED element in persistent storage
⠴ Processing command...
[22:10:00] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:00] [STDOUT] 
📝 NEW Feature Element: 
⠴ Processing command...
[22:10:00] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:00] [STDOUT] 
🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
⠴ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
[22:10:00] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠴ Processing command...
[22:10:00] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:00] [STDOUT] 
🖱️ Exploring clickable content element
⠴ Processing command...
[22:10:00] [RICH_CONSOLE] 🖱️ Exploring clickable content element
[22:10:02] [STDOUT] 
✅ Content element opened new screen
⠼ Processing command...
[22:10:02] [RICH_CONSOLE] ✅ Content element opened new screen
[22:10:02] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠼ Processing command...
[22:10:02] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:02] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠼ Processing command...
[22:10:02] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠼ Processing command...
[22:10:02] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠼ Processing command...
[22:10:02] [STDOUT] 
🚀 Found 1 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:02] [STDOUT] 
🚀 Found 20 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:02] [STDOUT] 
🚀 Found 27 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:02] [STDOUT] 
🚀 Found 47 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:02] [STDOUT] 
🚀 Found 57 elements using widgets strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:02] [STDOUT] 
🚀 Found 77 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:02] [STDOUT] 
✓ Parsed 14 unique elements from XML
⠦ Processing command...
[22:10:02] [STDOUT] 
✓ Classified 14 elements instantly
⠦ Processing command...
[22:10:02] [STDOUT] 
✓ Headless discovery found 14 elements
⠦ Processing command...
[22:10:02] [STDOUT] 
✅ Headless discovery: 14 elements
⠦ Processing command...
[22:10:02] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[22:10:02] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[22:10:02] [STDOUT] 
✅ Deep hierarchy parsing found 20 elements
⠦ Processing command...
[22:10:02] [STDOUT] 
✅ Deep parsing: 20 additional elements
⠦ Processing command...
[22:10:02] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[22:10:02] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[22:10:02] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[22:10:03] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠙ Processing command...
[22:10:03] [STDOUT] 
✅ Visual validation: additional elements found
⠙ Processing command...
[22:10:03] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 14 total elements in 0.51s
⠙ Processing command...
[22:10:03] [STDOUT] 
✅ Hybrid Headless Detection found 14 elements
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 14 elements
[22:10:03] [STDOUT] 
✅ Found 14 elements on new screen
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] ✅ Found 14 elements on new screen
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): text (confidence: 0.60)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): text (confidence: 0.60)
[22:10:03] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:03] [STDOUT] 
🔙 Returning to feature main screen
⠙ Processing command...
[22:10:03] [RICH_CONSOLE] 🔙 Returning to feature main screen
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): container (confidence: 0.40)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): container (confidence: 0.40)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): container (confidence: 0.40)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): container (confidence: 0.40)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): container (confidence: 0.40)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): container (confidence: 0.40)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): container (confidence: 0.40)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): container (confidence: 0.40)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): container (confidence: 0.40)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): container (confidence: 0.40)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:05] [STDOUT] 
🔄 UPDATED element: 
⠧ Processing command...
[22:10:05] [STDOUT] 
🔄 UPDATED element in persistent storage
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🔄 UPDATED element in persistent storage
[22:10:05] [STDOUT] 
📝 NEW Feature Element: 
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📝 NEW Feature Element: 
[22:10:05] [STDOUT] 
🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 FOUND MISSING SUB-ELEMENT:  in Ruang GTK
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:05] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:05] [STDOUT] 
📜 Attempting to scroll and discover hidden content
⠧ Processing command...
[22:10:05] [RICH_CONSOLE] 📜 Attempting to scroll and discover hidden content
[22:10:05] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[22:10:05] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:05] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[22:10:05] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[22:10:05] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[22:10:05] [STDOUT] 
🚀 Found 7 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:05] [STDOUT] 
🚀 Found 39 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:05] [STDOUT] 
🚀 Found 39 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:05] [STDOUT] 
🚀 Found 71 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:05] [STDOUT] 
🚀 Found 85 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:05] [STDOUT] 
🚀 Found 117 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:05] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠋ Processing command...
[22:10:05] [STDOUT] 
✓ Classified 15 elements instantly
⠋ Processing command...
[22:10:05] [STDOUT] 
✓ Headless discovery found 15 elements
⠋ Processing command...
[22:10:05] [STDOUT] 
✅ Headless discovery: 15 elements
⠋ Processing command...
[22:10:05] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[22:10:05] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[22:10:05] [STDOUT] 
✅ Deep hierarchy parsing found 32 elements
⠋ Processing command...
[22:10:05] [STDOUT] 
✅ Deep parsing: 32 additional elements
⠋ Processing command...
[22:10:05] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[22:10:05] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[22:10:05] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[22:10:05] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[22:10:05] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[22:10:05] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.45s
⠼ Processing command...
[22:10:05] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠼ Processing command...
[22:10:05] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[22:10:05] [STDOUT] 
📜 Performing coordinate-free scroll (attempt 1)
⠼ Processing command...
[22:10:05] [RICH_CONSOLE] 📜 Performing coordinate-free scroll (attempt 1)
[22:10:06] [STDOUT] 
✅ Used scrollable element for scrolling
⠸ Processing command...
[22:10:06] [RICH_CONSOLE] ✅ Used scrollable element for scrolling
[22:10:07] [STDOUT] 
⚠️ Scroll didn't change screen content - stopping scroll attempts
⠦ Processing command...
[22:10:07] [RICH_CONSOLE] ⚠️ Scroll didn't change screen content - stopping scroll attempts
[22:10:07] [STDOUT] 
✅ Strategy 1 completed: 25 elements found, 2 interactions
⠦ Processing command...
[22:10:07] [RICH_CONSOLE] ✅ Strategy 1 completed: 25 elements found, 2 interactions
[22:10:07] [STDOUT] 
✅ Strategy 1 completed: 25 elements found
⠦ Processing command...
[22:10:07] [RICH_CONSOLE] ✅ Strategy 1 completed: 25 elements found
[22:10:07] [STDOUT] 
📊 Strategy 2: Comprehensive content discovery in 'Ruang GTK'
⠧ Processing command...
[22:10:07] [RICH_CONSOLE] 📊 Strategy 2: Comprehensive content discovery in 'Ruang GTK'
[22:10:07] [STDOUT] 
🔍 Strategy 2: Enhanced discovery in 'Ruang GTK' for missing sub-elements
⠧ Processing command...
[22:10:07] [RICH_CONSOLE] 🔍 Strategy 2: Enhanced discovery in 'Ruang GTK' for missing sub-elements
[22:10:07] [STDOUT] 
🗂️ Strategy 2: Tab and navigation element discovery
⠧ Processing command...
[22:10:07] [RICH_CONSOLE] 🗂️ Strategy 2: Tab and navigation element discovery
[22:10:07] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠧ Processing command...
[22:10:07] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:07] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠧ Processing command...
[22:10:07] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠧ Processing command...
[22:10:07] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠧ Processing command...
[22:10:07] [STDOUT] 
🚀 Found 7 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:07] [STDOUT] 
🚀 Found 39 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:07] [STDOUT] 
🚀 Found 39 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:07] [STDOUT] 
🚀 Found 71 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:07] [STDOUT] 
🚀 Found 85 elements using widgets strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:07] [STDOUT] 
🚀 Found 117 elements using fallback strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:07] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠇ Processing command...
[22:10:07] [STDOUT] 
✓ Classified 15 elements instantly
⠇ Processing command...
[22:10:07] [STDOUT] 
✓ Headless discovery found 15 elements
⠇ Processing command...
[22:10:07] [STDOUT] 
✅ Headless discovery: 15 elements
⠇ Processing command...
[22:10:07] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠇ Processing command...
[22:10:07] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠇ Processing command...
[22:10:07] [STDOUT] 
✅ Deep hierarchy parsing found 32 elements
⠇ Processing command...
[22:10:07] [STDOUT] 
✅ Deep parsing: 32 additional elements
⠇ Processing command...
[22:10:07] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[22:10:07] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[22:10:07] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[22:10:08] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[22:10:08] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[22:10:08] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.45s
⠹ Processing command...
[22:10:08] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:08] [STDOUT] 
✅ Found 0 navigation elements in feature
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Found 0 navigation elements in feature
[22:10:08] [STDOUT] 
✅ Strategy 2 completed: 0 elements found, 0 interactions
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Strategy 2 completed: 0 elements found, 0 interactions
[22:10:08] [STDOUT] 
✅ Strategy 2 completed: 0 elements found
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Strategy 2 completed: 0 elements found
[22:10:08] [STDOUT] 
📊 Strategy 3: Comprehensive content discovery in 'Ruang GTK'
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 📊 Strategy 3: Comprehensive content discovery in 'Ruang GTK'
[22:10:08] [STDOUT] 
🔍 Strategy 3: Enhanced discovery in 'Ruang GTK' for missing sub-elements
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 🔍 Strategy 3: Enhanced discovery in 'Ruang GTK' for missing sub-elements
[22:10:08] [STDOUT] 
⚡ Strategy 3: Deep interaction and form discovery
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] ⚡ Strategy 3: Deep interaction and form discovery
[22:10:08] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[22:10:08] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:08] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[22:10:08] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[22:10:08] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[22:10:08] [STDOUT] 
🚀 Found 7 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:08] [STDOUT] 
🚀 Found 39 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:08] [STDOUT] 
🚀 Found 39 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:08] [STDOUT] 
🚀 Found 71 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:08] [STDOUT] 
🚀 Found 85 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:08] [STDOUT] 
🚀 Found 117 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:08] [STDOUT] 
✓ Parsed 15 unique elements from XML
⠼ Processing command...
[22:10:08] [STDOUT] 
✓ Classified 15 elements instantly
⠼ Processing command...
[22:10:08] [STDOUT] 
✓ Headless discovery found 15 elements
⠼ Processing command...
[22:10:08] [STDOUT] 
✅ Headless discovery: 15 elements
⠼ Processing command...
[22:10:08] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[22:10:08] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[22:10:08] [STDOUT] 
✅ Deep hierarchy parsing found 32 elements
⠼ Processing command...
[22:10:08] [STDOUT] 
✅ Deep parsing: 32 additional elements
⠼ Processing command...
[22:10:08] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠼ Processing command...
[22:10:08] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠼ Processing command...
[22:10:08] [STDOUT] 
🎯 Performing targeted visual scan...
⠼ Processing command...
[22:10:08] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠇ Processing command...
[22:10:08] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[22:10:08] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 15 total elements in 0.45s
⠇ Processing command...
[22:10:08] [STDOUT] 
✅ Hybrid Headless Detection found 15 elements
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 15 elements
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): element (confidence: 0.40)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): element (confidence: 0.40)
[22:10:08] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:08] [STDOUT] 
✅ Found 0 deep interactive elements in feature
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Found 0 deep interactive elements in feature
[22:10:08] [STDOUT] 
✅ Strategy 3 completed: 0 elements found, 0 interactions
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Strategy 3 completed: 0 elements found, 0 interactions
[22:10:08] [STDOUT] 
✅ Strategy 3 completed: 0 elements found
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ Strategy 3 completed: 0 elements found
[22:10:08] [STDOUT] 
✅ FEATURE ANALYSIS COMPLETE for 'Ruang GTK'
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] ✅ FEATURE ANALYSIS COMPLETE for 'Ruang GTK'
[22:10:08] [STDOUT] 
📊 Total elements discovered: 25
⠇ Processing command...
[22:10:08] [RICH_CONSOLE] 📊 Total elements discovered: 25
[22:10:08] [STDOUT] 
🔗 Total interactions performed: 2
⠏ Processing command...
[22:10:08] [RICH_CONSOLE] 🔗 Total interactions performed: 2
[22:10:08] [STDOUT] 
📏 Maximum depth explored: 2
⠏ Processing command...
[22:10:08] [RICH_CONSOLE] 📏 Maximum depth explored: 2
[22:10:08] [STDOUT] 
🔙 Returning to main page after completing 'Ruang GTK' analysis
⠏ Processing command...
[22:10:08] [RICH_CONSOLE] 🔙 Returning to main page after completing 'Ruang GTK' analysis
[22:10:08] [STDOUT] 
🔙 Returning to main page after analyzing 'Ruang GTK'
⠏ Processing command...
[22:10:08] [RICH_CONSOLE] 🔙 Returning to main page after analyzing 'Ruang GTK'
[22:10:08] [STDOUT] 
🔙 Back navigation attempt 1/3
⠙ Processing command...
[22:10:08] [RICH_CONSOLE] 🔙 Back navigation attempt 1/3
[22:10:11] [STDOUT] 
✅ Back navigation successful (screen changed)
⠋ Processing command...
[22:10:11] [RICH_CONSOLE] ✅ Back navigation successful (screen changed)
[22:10:11] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[22:10:11] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:11] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[22:10:11] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[22:10:11] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[22:10:11] [STDOUT] 
🚀 Found 14 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:11] [STDOUT] 
🚀 Found 48 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:11] [STDOUT] 
🚀 Found 48 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:11] [STDOUT] 
🚀 Found 82 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:11] [STDOUT] 
🚀 Found 101 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:11] [STDOUT] 
🚀 Found 135 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:11] [STDOUT] 
✓ Parsed 22 unique elements from XML
⠙ Processing command...
[22:10:11] [STDOUT] 
✓ Classified 22 elements instantly
⠙ Processing command...
[22:10:11] [STDOUT] 
✓ Headless discovery found 22 elements
⠙ Processing command...
[22:10:11] [STDOUT] 
✅ Headless discovery: 22 elements
⠙ Processing command...
[22:10:11] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[22:10:11] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[22:10:11] [STDOUT] 
✅ Deep hierarchy parsing found 34 elements
⠙ Processing command...
[22:10:11] [STDOUT] 
✅ Deep parsing: 34 additional elements
⠙ Processing command...
[22:10:11] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[22:10:11] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[22:10:11] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[22:10:11] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[22:10:11] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[22:10:11] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 22 total elements in 0.61s
⠧ Processing command...
[22:10:11] [STDOUT] 
✅ Hybrid Headless Detection found 22 elements
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 22 elements
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:11] [STDOUT] 
🎯 Enhanced classification: list (confidence: 0.70)
⠧ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification: list (confidence: 0.70)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:11] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠇ Processing command...
[22:10:11] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:11] [STDOUT] 
📊 Main feature indicators found: 0 (main page: False)
⠇ Processing command...
[22:10:11] [RICH_CONSOLE] 📊 Main feature indicators found: 0 (main page: False)
[22:10:11] [STDOUT] 
⚠️ Screen changed but main features not visible, continuing back navigation
⠇ Processing command...
[22:10:11] [RICH_CONSOLE] ⚠️ Screen changed but main features not visible, continuing back navigation
[22:10:12] [STDOUT] 
🔙 Back navigation attempt 2/3
⠋ Processing command...
[22:10:12] [RICH_CONSOLE] 🔙 Back navigation attempt 2/3
[22:10:14] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher
⠇ Processing command...
[22:10:14] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher
[22:10:14] [STDOUT] 
🔄 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠇ Processing command...
[22:10:14] [RICH_CONSOLE] 🔄 Returning to main app: com.kemendikdasmen.rumahpendidikan
[22:10:14] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠇ Processing command...
[22:10:14] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[22:10:14] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[22:10:14] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[22:10:14] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[22:10:14] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[22:10:14] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠙ Processing command...
[22:10:14] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[22:10:14] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[22:10:14] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[22:10:16] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[22:10:16] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[22:10:19] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[22:10:19] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[22:10:21] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[22:10:21] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:21] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[22:10:21] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[22:10:21] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[22:10:21] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:21] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:21] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:21] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:21] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:21] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:21] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠼ Processing command...
[22:10:21] [STDOUT] 
✓ Classified 10 elements instantly
⠼ Processing command...
[22:10:21] [STDOUT] 
✓ Headless discovery found 10 elements
⠼ Processing command...
[22:10:21] [STDOUT] 
✅ Headless discovery: 10 elements
⠼ Processing command...
[22:10:21] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[22:10:21] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[22:10:21] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠼ Processing command...
[22:10:21] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠼ Processing command...
[22:10:21] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠼ Processing command...
[22:10:21] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠼ Processing command...
[22:10:21] [STDOUT] 
🎯 Performing targeted visual scan...
⠼ Processing command...
[22:10:22] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠇ Processing command...
[22:10:22] [STDOUT] 
✅ Visual validation: additional elements found
⠇ Processing command...
[22:10:22] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.45s
⠇ Processing command...
[22:10:22] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠇ Processing command...
[22:10:22] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:22] [STDOUT] 
✅ Main app restarted and ready with 10 elements
⠇ Processing command...
[22:10:22] [RICH_CONSOLE] ✅ Main app restarted and ready with 10 elements
[22:10:22] [STDOUT] 
✅ Main app restarted successfully
⠇ Processing command...
[22:10:22] [RICH_CONSOLE] ✅ Main app restarted successfully
[22:10:24] [STDOUT] 
✅ COMPLETED: Feature 'Ruang GTK' - Successfully analyzed (25 elements, 2 interactions)
⠸ Processing command...
[22:10:24] [RICH_CONSOLE] ✅ COMPLETED: Feature 'Ruang GTK' - Successfully analyzed (25 elements, 2 interactions)
[22:10:24] [STDOUT] 
📊 Feature Ruang GTK completed:
⠸ Processing command...
[22:10:24] [RICH_CONSOLE] 📊 Feature Ruang GTK completed:
[22:10:24] [STDOUT] 
   Elements: 25
⠸ Processing command...
[22:10:24] [RICH_CONSOLE]    Elements: 25
[22:10:24] [STDOUT] 
   Interactions: 2
⠸ Processing command...
[22:10:24] [RICH_CONSOLE]    Interactions: 2
[22:10:24] [STDOUT] 
   Sub-features: 0
⠸ Processing command...
[22:10:24] [RICH_CONSOLE]    Sub-features: 0
[22:10:24] [STDOUT] 
📊 Sub-features: 0, Interactions: 2
⠸ Processing command...
[22:10:24] [RICH_CONSOLE] 📊 Sub-features: 0, Interactions: 2
[22:10:24] [STDOUT] 
🏥 Post-feature health check for 'Ruang GTK'...
⠸ Processing command...
[22:10:24] [RICH_CONSOLE] 🏥 Post-feature health check for 'Ruang GTK'...
[22:10:24] [STDOUT] 
🏥 Checking application health...
⠸ Processing command...
[22:10:24] [RICH_CONSOLE] 🏥 Checking application health...
[22:10:24] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[22:10:24] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:24] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[22:10:24] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[22:10:24] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[22:10:24] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:24] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:24] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:24] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:24] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:24] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:24] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠧ Processing command...
[22:10:24] [STDOUT] 
✓ Classified 10 elements instantly
⠧ Processing command...
[22:10:24] [STDOUT] 
✓ Headless discovery found 10 elements
⠧ Processing command...
[22:10:24] [STDOUT] 
✅ Headless discovery: 10 elements
⠧ Processing command...
[22:10:24] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[22:10:24] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[22:10:24] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠧ Processing command...
[22:10:24] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠧ Processing command...
[22:10:24] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[22:10:24] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[22:10:24] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[22:10:24] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠙ Processing command...
[22:10:24] [STDOUT] 
✅ Visual validation: additional elements found
⠙ Processing command...
[22:10:24] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.44s
⠙ Processing command...
[22:10:24] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠙ Processing command...
[22:10:24] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:25] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠸ Processing command...
[22:10:25] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:25] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠼ Processing command...
[22:10:25] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:25] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠼ Processing command...
[22:10:25] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠼ Processing command...
[22:10:25] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠼ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:25] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠴ Processing command...
[22:10:25] [STDOUT] 
✓ Classified 10 elements instantly
⠴ Processing command...
[22:10:25] [STDOUT] 
✓ Headless discovery found 10 elements
⠴ Processing command...
[22:10:25] [STDOUT] 
✅ Headless discovery: 10 elements
⠴ Processing command...
[22:10:25] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠴ Processing command...
[22:10:25] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠴ Processing command...
[22:10:25] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠴ Processing command...
[22:10:25] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠴ Processing command...
[22:10:25] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[22:10:25] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[22:10:25] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[22:10:25] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[22:10:25] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[22:10:25] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.36s
⠏ Processing command...
[22:10:25] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:25] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:25] [STDOUT] 
✅ App is responsive
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] ✅ App is responsive
[22:10:25] [STDOUT] 
🔄 CRITICAL: Ensuring return to main page before next feature...
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🔄 CRITICAL: Ensuring return to main page before next feature...
[22:10:25] [STDOUT] 
🔙 Ensuring return to main page...
⠏ Processing command...
[22:10:25] [RICH_CONSOLE] 🔙 Ensuring return to main page...
[22:10:25] [STDOUT] 
✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
⠙ Processing command...
[22:10:25] [RICH_CONSOLE] ✅ Already in main app (com.kemendikdasmen.rumahpendidikan), using back navigation...
[22:10:25] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠙ Processing command...
[22:10:25] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:25] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠙ Processing command...
[22:10:25] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠙ Processing command...
[22:10:25] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠙ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:25] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[22:10:25] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠙ Processing command...
[22:10:25] [STDOUT] 
✓ Classified 10 elements instantly
⠙ Processing command...
[22:10:25] [STDOUT] 
✓ Headless discovery found 10 elements
⠙ Processing command...
[22:10:25] [STDOUT] 
✅ Headless discovery: 10 elements
⠙ Processing command...
[22:10:25] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[22:10:25] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[22:10:25] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠙ Processing command...
[22:10:25] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠙ Processing command...
[22:10:25] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠙ Processing command...
[22:10:25] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠙ Processing command...
[22:10:25] [STDOUT] 
🎯 Performing targeted visual scan...
⠙ Processing command...
[22:10:26] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[22:10:26] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[22:10:26] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.36s
⠦ Processing command...
[22:10:26] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:26] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:26] [STDOUT] 
🔙 Back navigation attempt 1/5 (in main app)
⠦ Processing command...
[22:10:26] [RICH_CONSOLE] 🔙 Back navigation attempt 1/5 (in main app)
[22:10:27] [STDOUT] 
⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
⠋ Processing command...
[22:10:27] [RICH_CONSOLE] ⚠️ Back navigation took us out of main app to com.google.android.apps.nexuslauncher, stopping back navigation
[22:10:27] [STDOUT] 
🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[22:10:27] [RICH_CONSOLE] 🔙 Returning to main app: com.kemendikdasmen.rumahpendidikan
[22:10:28] [STDOUT] 
🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[22:10:28] [RICH_CONSOLE] 🔄 Attempt 1/8: Current app is com.google.android.apps.nexuslauncher, target is com.kemendikdasmen.rumahpendidikan
[22:10:28] [STDOUT] 
🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[22:10:28] [RICH_CONSOLE] 🚀 Switching to main app directly: com.kemendikdasmen.rumahpendidikan
[22:10:28] [STDOUT] 
⚠️ Main app not running, attempting restart...
⠹ Processing command...
[22:10:28] [RICH_CONSOLE] ⚠️ Main app not running, attempting restart...
[22:10:28] [STDOUT] 
🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[22:10:28] [RICH_CONSOLE] 🔄 Restarting main app: com.kemendikdasmen.rumahpendidikan
[22:10:30] [STDOUT] 
📱 Stopped app: com.kemendikdasmen.rumahpendidikan
⠇ Processing command...
[22:10:30] [RICH_CONSOLE] 📱 Stopped app: com.kemendikdasmen.rumahpendidikan
[22:10:33] [STDOUT] 
🚀 Started app: com.kemendikdasmen.rumahpendidikan
⠇ Processing command...
[22:10:33] [RICH_CONSOLE] 🚀 Started app: com.kemendikdasmen.rumahpendidikan
[22:10:35] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠼ Processing command...
[22:10:35] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:35] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠼ Processing command...
[22:10:35] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠼ Processing command...
[22:10:35] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠼ Processing command...
[22:10:35] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:35] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:35] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:35] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:35] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:35] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:35] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠦ Processing command...
[22:10:35] [STDOUT] 
✓ Classified 10 elements instantly
⠦ Processing command...
[22:10:35] [STDOUT] 
✓ Headless discovery found 10 elements
⠦ Processing command...
[22:10:35] [STDOUT] 
✅ Headless discovery: 10 elements
⠦ Processing command...
[22:10:35] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[22:10:35] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[22:10:35] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠦ Processing command...
[22:10:35] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠦ Processing command...
[22:10:35] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[22:10:35] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[22:10:35] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[22:10:36] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[22:10:36] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[22:10:36] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.45s
⠋ Processing command...
[22:10:36] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:36] [STDOUT] 
✅ Main app restarted and ready with 10 elements
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ✅ Main app restarted and ready with 10 elements
[22:10:36] [STDOUT] 
✅ Main app restarted successfully
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ✅ Main app restarted successfully
[22:10:36] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[22:10:36] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:36] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[22:10:36] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[22:10:36] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[22:10:36] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:36] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:36] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:36] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:36] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:36] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:36] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠼ Processing command...
[22:10:36] [STDOUT] 
✓ Classified 10 elements instantly
⠼ Processing command...
[22:10:36] [STDOUT] 
✓ Headless discovery found 10 elements
⠼ Processing command...
[22:10:36] [STDOUT] 
✅ Headless discovery: 10 elements
⠼ Processing command...
[22:10:36] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[22:10:36] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[22:10:36] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠴ Processing command...
[22:10:36] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠴ Processing command...
[22:10:36] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[22:10:36] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[22:10:36] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[22:10:36] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[22:10:36] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[22:10:36] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.52s
⠏ Processing command...
[22:10:36] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠏ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:36] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:36] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:36] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:36] [STDOUT] 
⚠️ Main page verification failed - no main features or indicators found
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ Main page verification failed - no main features or indicators found
[22:10:36] [STDOUT] 
⚠️ Not on main page, trying force return...
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] ⚠️ Not on main page, trying force return...
[22:10:36] [STDOUT] 
🔄 Force returning to main page...
⠋ Processing command...
[22:10:36] [RICH_CONSOLE] 🔄 Force returning to main page...
[22:10:36] [STDOUT] 
✅ Already in main app, using enhanced universal back navigation to return to main page...
⠹ Processing command...
[22:10:36] [RICH_CONSOLE] ✅ Already in main app, using enhanced universal back navigation to return to main page...
[22:10:36] [STDOUT] 
🎯 === UNIVERSAL GOAL-ORIENTED BACK NAVIGATION ===
⠹ Processing command...
[22:10:36] [RICH_CONSOLE] 🎯 === UNIVERSAL GOAL-ORIENTED BACK NAVIGATION ===
[22:10:36] [STDOUT] 
📋 Target: main page
⠹ Processing command...
[22:10:36] [RICH_CONSOLE] 📋 Target: main page
[22:10:36] [STDOUT] 
📍 Context: return_to_main_page_in_app
⠹ Processing command...
[22:10:36] [RICH_CONSOLE] 📍 Context: return_to_main_page_in_app
[22:10:36] [STDOUT] 
🔧 Config: max_attempts=5, sleep=1s, timeout=3s
⠹ Processing command...
[22:10:36] [RICH_CONSOLE] 🔧 Config: max_attempts=5, sleep=1s, timeout=3s
[22:10:36] [STDOUT] 
🔍 PRE-CHECK: Verifying if already at 'main page'
⠹ Processing command...
[22:10:36] [RICH_CONSOLE] 🔍 PRE-CHECK: Verifying if already at 'main page'
[22:10:37] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠼ Processing command...
[22:10:37] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:37] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠼ Processing command...
[22:10:37] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠼ Processing command...
[22:10:37] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠼ Processing command...
[22:10:37] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:37] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:37] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:37] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:37] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:37] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:37] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠴ Processing command...
[22:10:37] [STDOUT] 
✓ Classified 10 elements instantly
⠴ Processing command...
[22:10:37] [STDOUT] 
✓ Headless discovery found 10 elements
⠴ Processing command...
[22:10:37] [STDOUT] 
✅ Headless discovery: 10 elements
⠴ Processing command...
[22:10:37] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠴ Processing command...
[22:10:37] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠴ Processing command...
[22:10:37] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠦ Processing command...
[22:10:37] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠦ Processing command...
[22:10:37] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[22:10:37] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[22:10:37] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[22:10:37] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[22:10:37] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[22:10:37] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.51s
⠋ Processing command...
[22:10:37] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠋ Processing command...
[22:10:37] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠋ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠋ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠋ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠋ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:10:37] [STDOUT] 
🔄 Session duplicate:  - ANALYZING for comprehensive coverage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🔄 Session duplicate:  - ANALYZING for comprehensive coverage
[22:10:37] [STDOUT] 
⏭️ SKIPPING: Element already exists in persistent storage
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⏭️ SKIPPING: Element already exists in persistent storage
[22:10:37] [STDOUT] 
⚠️ FILTERED: Too short text '' excluded from features
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ FILTERED: Too short text '' excluded from features
[22:10:37] [STDOUT] 
⚠️ Main page verification failed - no main features or indicators found
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] ⚠️ Main page verification failed - no main features or indicators found
[22:10:37] [STDOUT] 
🔙 === BACK NAVIGATION ATTEMPT 1/5 ===
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 
🔙 === BACK NAVIGATION ATTEMPT 1/5 ===
[22:10:37] [STDOUT] 
🎯 Goal: Reach 'main page'
⠙ Processing command...
[22:10:37] [RICH_CONSOLE] 🎯 Goal: Reach 'main page'
[22:10:37] [STDOUT] 
👆 Executing back press #1
⠸ Processing command...
[22:10:37] [RICH_CONSOLE] 👆 Executing back press #1
[22:10:39] [STDOUT] 
📱 POST-BACK CHECK: Verifying app is still running
⠴ Processing command...
[22:10:39] [RICH_CONSOLE] 📱 POST-BACK CHECK: Verifying app is still running
[22:10:39] [STDOUT] 
🚨 APP CLOSED after back press 1!
⠧ Processing command...
[22:10:39] [RICH_CONSOLE] 🚨 APP CLOSED after back press 1!
[22:10:39] [STDOUT] 
📱 Current app: com.google.android.apps.nexuslauncher
⠧ Processing command...
[22:10:39] [RICH_CONSOLE] 📱 Current app: com.google.android.apps.nexuslauncher
[22:10:39] [STDOUT] 
🔄 Goal 'main page' failed - app recovery needed
⠧ Processing command...
[22:10:39] [RICH_CONSOLE] 🔄 Goal 'main page' failed - app recovery needed
[22:10:39] [STDOUT] 
🚨 Force return failed, attempting robust app recovery...
⠧ Processing command...
[22:10:39] [RICH_CONSOLE] 🚨 Force return failed, attempting robust app recovery...
[22:10:39] [STDOUT] 
🔄 ROBUST APP RECOVERY: Attempting to recover main application
⠧ Processing command...
[22:10:39] [RICH_CONSOLE] 🔄 ROBUST APP RECOVERY: Attempting to recover main application
[22:10:39] [STDOUT] 
📱 Step 1: Cleaning up existing app instances...
⠧ Processing command...
[22:10:39] [RICH_CONSOLE] 📱 Step 1: Cleaning up existing app instances...
[22:10:41] [STDOUT] 
✅ App instances cleaned up
⠸ Processing command...
[22:10:41] [RICH_CONSOLE] ✅ App instances cleaned up
[22:10:41] [STDOUT] 
📱 Step 2: Returning to home screen...
⠸ Processing command...
[22:10:41] [RICH_CONSOLE] 📱 Step 2: Returning to home screen...
[22:10:44] [STDOUT] 
📱 Step 3: Launching main app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[22:10:44] [RICH_CONSOLE] 📱 Step 3: Launching main app: com.kemendikdasmen.rumahpendidikan
[22:10:45] [STDOUT] 
✅ App launch command sent
⠸ Processing command...
[22:10:45] [RICH_CONSOLE] ✅ App launch command sent
[22:10:45] [STDOUT] 
📱 Step 4: Waiting dynamically for app to be ready...
⠸ Processing command...
[22:10:45] [RICH_CONSOLE] 📱 Step 4: Waiting dynamically for app to be ready...
[22:10:45] [STDOUT] 
⏳ Waiting for app to be ready (max 20s)...
⠸ Processing command...
[22:10:45] [RICH_CONSOLE] ⏳ Waiting for app to be ready (max 20s)...
[22:10:45] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠹ Processing command...
[22:10:45] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:45] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[22:10:45] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:45] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[22:10:45] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[22:10:45] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[22:10:46] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:46] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:46] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:46] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:46] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:46] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠴ Processing command...
[22:10:46] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠴ Processing command...
[22:10:46] [STDOUT] 
✓ Classified 10 elements instantly
⠴ Processing command...
[22:10:46] [STDOUT] 
✓ Headless discovery found 10 elements
⠴ Processing command...
[22:10:46] [STDOUT] 
✅ Headless discovery: 10 elements
⠴ Processing command...
[22:10:46] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠴ Processing command...
[22:10:46] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠴ Processing command...
[22:10:46] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠧ Processing command...
[22:10:46] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠧ Processing command...
[22:10:46] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[22:10:46] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[22:10:46] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[22:10:46] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[22:10:46] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[22:10:46] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.91s
⠼ Processing command...
[22:10:46] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠼ Processing command...
[22:10:46] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:46] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠼ Processing command...
[22:10:46] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:46] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠴ Processing command...
[22:10:46] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:46] [STDOUT] 
⏳ Still in loading screens, waiting...
⠴ Processing command...
[22:10:46] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:47] [STDOUT] 
⏳ Elapsed: 2.7s / 20s
⠧ Processing command...
[22:10:47] [RICH_CONSOLE] ⏳ Elapsed: 2.7s / 20s
[22:10:47] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[22:10:47] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:47] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[22:10:47] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:47] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[22:10:47] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[22:10:47] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[22:10:47] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:47] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:47] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:47] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:47] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:47] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:47] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠏ Processing command...
[22:10:47] [STDOUT] 
✓ Classified 10 elements instantly
⠏ Processing command...
[22:10:47] [STDOUT] 
✓ Headless discovery found 10 elements
⠏ Processing command...
[22:10:47] [STDOUT] 
✅ Headless discovery: 10 elements
⠏ Processing command...
[22:10:47] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[22:10:47] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[22:10:47] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠏ Processing command...
[22:10:47] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠏ Processing command...
[22:10:47] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠏ Processing command...
[22:10:47] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠏ Processing command...
[22:10:47] [STDOUT] 
🎯 Performing targeted visual scan...
⠏ Processing command...
[22:10:48] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[22:10:48] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[22:10:48] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.37s
⠼ Processing command...
[22:10:48] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠼ Processing command...
[22:10:48] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:48] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠼ Processing command...
[22:10:48] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:48] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠴ Processing command...
[22:10:48] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:48] [STDOUT] 
⏳ Still in loading screens, waiting...
⠴ Processing command...
[22:10:48] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:49] [STDOUT] 
⏳ Elapsed: 4.3s / 20s
⠇ Processing command...
[22:10:49] [RICH_CONSOLE] ⏳ Elapsed: 4.3s / 20s
[22:10:49] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠏ Processing command...
[22:10:49] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:49] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠏ Processing command...
[22:10:49] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:49] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠏ Processing command...
[22:10:49] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠏ Processing command...
[22:10:49] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠏ Processing command...
[22:10:49] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:49] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:49] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:49] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:49] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:49] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[22:10:49] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠋ Processing command...
[22:10:49] [STDOUT] 
✓ Classified 10 elements instantly
⠋ Processing command...
[22:10:49] [STDOUT] 
✓ Headless discovery found 10 elements
⠋ Processing command...
[22:10:49] [STDOUT] 
✅ Headless discovery: 10 elements
⠋ Processing command...
[22:10:49] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[22:10:49] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[22:10:49] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠹ Processing command...
[22:10:49] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠹ Processing command...
[22:10:49] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[22:10:49] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[22:10:49] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[22:10:50] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠦ Processing command...
[22:10:50] [STDOUT] 
✅ Visual validation: additional elements found
⠦ Processing command...
[22:10:50] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.54s
⠦ Processing command...
[22:10:50] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠦ Processing command...
[22:10:50] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:50] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠦ Processing command...
[22:10:50] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:50] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠧ Processing command...
[22:10:50] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:50] [STDOUT] 
⏳ Still in loading screens, waiting...
⠧ Processing command...
[22:10:50] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:51] [STDOUT] 
⏳ Elapsed: 6.1s / 20s
⠋ Processing command...
[22:10:51] [RICH_CONSOLE] ⏳ Elapsed: 6.1s / 20s
[22:10:51] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠹ Processing command...
[22:10:51] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:51] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[22:10:51] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:51] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[22:10:51] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[22:10:51] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[22:10:51] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:51] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:51] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:51] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:51] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:51] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:51] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠸ Processing command...
[22:10:51] [STDOUT] 
✓ Classified 10 elements instantly
⠸ Processing command...
[22:10:51] [STDOUT] 
✓ Headless discovery found 10 elements
⠸ Processing command...
[22:10:51] [STDOUT] 
✅ Headless discovery: 10 elements
⠸ Processing command...
[22:10:51] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[22:10:51] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[22:10:51] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠸ Processing command...
[22:10:51] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠸ Processing command...
[22:10:51] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[22:10:51] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[22:10:51] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[22:10:51] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[22:10:51] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[22:10:51] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.42s
⠧ Processing command...
[22:10:51] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠧ Processing command...
[22:10:51] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:51] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠧ Processing command...
[22:10:51] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:51] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠇ Processing command...
[22:10:51] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:51] [STDOUT] 
⏳ Still in loading screens, waiting...
⠇ Processing command...
[22:10:51] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:52] [STDOUT] 
⏳ Elapsed: 7.8s / 20s
⠙ Processing command...
[22:10:52] [RICH_CONSOLE] ⏳ Elapsed: 7.8s / 20s
[22:10:53] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠹ Processing command...
[22:10:53] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:53] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[22:10:53] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:53] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[22:10:53] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[22:10:53] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[22:10:53] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:53] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:53] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:53] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:10:53] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:53] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[22:10:53] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠼ Processing command...
[22:10:53] [STDOUT] 
✓ Classified 10 elements instantly
⠼ Processing command...
[22:10:53] [STDOUT] 
✓ Headless discovery found 10 elements
⠼ Processing command...
[22:10:53] [STDOUT] 
✅ Headless discovery: 10 elements
⠼ Processing command...
[22:10:53] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[22:10:53] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[22:10:53] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠴ Processing command...
[22:10:53] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠴ Processing command...
[22:10:53] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[22:10:53] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[22:10:53] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[22:10:53] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[22:10:53] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[22:10:53] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.53s
⠏ Processing command...
[22:10:53] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠏ Processing command...
[22:10:53] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:53] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠏ Processing command...
[22:10:53] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:53] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠋ Processing command...
[22:10:53] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:53] [STDOUT] 
⏳ Still in loading screens, waiting...
⠋ Processing command...
[22:10:53] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:54] [STDOUT] 
⏳ Elapsed: 9.6s / 20s
⠸ Processing command...
[22:10:54] [RICH_CONSOLE] ⏳ Elapsed: 9.6s / 20s
[22:10:54] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠴ Processing command...
[22:10:54] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:54] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[22:10:54] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:54] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[22:10:54] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[22:10:54] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[22:10:54] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:54] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:54] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:54] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:54] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:54] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[22:10:54] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠦ Processing command...
[22:10:54] [STDOUT] 
✓ Classified 10 elements instantly
⠦ Processing command...
[22:10:54] [STDOUT] 
✓ Headless discovery found 10 elements
⠦ Processing command...
[22:10:54] [STDOUT] 
✅ Headless discovery: 10 elements
⠦ Processing command...
[22:10:54] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[22:10:54] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[22:10:54] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠦ Processing command...
[22:10:54] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠦ Processing command...
[22:10:54] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠦ Processing command...
[22:10:54] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠦ Processing command...
[22:10:54] [STDOUT] 
🎯 Performing targeted visual scan...
⠦ Processing command...
[22:10:55] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠋ Processing command...
[22:10:55] [STDOUT] 
✅ Visual validation: additional elements found
⠋ Processing command...
[22:10:55] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.43s
⠋ Processing command...
[22:10:55] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠋ Processing command...
[22:10:55] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:55] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠋ Processing command...
[22:10:55] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:55] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠹ Processing command...
[22:10:55] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:55] [STDOUT] 
⏳ Still in loading screens, waiting...
⠹ Processing command...
[22:10:55] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:56] [STDOUT] 
⏳ Elapsed: 11.3s / 20s
⠼ Processing command...
[22:10:56] [RICH_CONSOLE] ⏳ Elapsed: 11.3s / 20s
[22:10:56] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[22:10:56] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:56] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠦ Processing command...
[22:10:56] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:56] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠦ Processing command...
[22:10:56] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠦ Processing command...
[22:10:56] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠦ Processing command...
[22:10:56] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:56] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:56] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:56] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:56] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:56] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[22:10:56] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠧ Processing command...
[22:10:56] [STDOUT] 
✓ Classified 10 elements instantly
⠧ Processing command...
[22:10:56] [STDOUT] 
✓ Headless discovery found 10 elements
⠧ Processing command...
[22:10:56] [STDOUT] 
✅ Headless discovery: 10 elements
⠧ Processing command...
[22:10:56] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[22:10:56] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[22:10:56] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠇ Processing command...
[22:10:56] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠇ Processing command...
[22:10:56] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[22:10:56] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[22:10:56] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[22:10:56] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[22:10:56] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[22:10:56] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.50s
⠹ Processing command...
[22:10:56] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠹ Processing command...
[22:10:56] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:56] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠹ Processing command...
[22:10:56] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:57] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠸ Processing command...
[22:10:57] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:57] [STDOUT] 
⏳ Still in loading screens, waiting...
⠸ Processing command...
[22:10:57] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:58] [STDOUT] 
⏳ Elapsed: 13.0s / 20s
⠦ Processing command...
[22:10:58] [RICH_CONSOLE] ⏳ Elapsed: 13.0s / 20s
[22:10:58] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠧ Processing command...
[22:10:58] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:58] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠧ Processing command...
[22:10:58] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:58] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠧ Processing command...
[22:10:58] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠧ Processing command...
[22:10:58] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠧ Processing command...
[22:10:58] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:58] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:58] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:58] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠇ Processing command...
[22:10:58] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:58] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠏ Processing command...
[22:10:58] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠏ Processing command...
[22:10:58] [STDOUT] 
✓ Classified 10 elements instantly
⠏ Processing command...
[22:10:58] [STDOUT] 
✓ Headless discovery found 10 elements
⠏ Processing command...
[22:10:58] [STDOUT] 
✅ Headless discovery: 10 elements
⠏ Processing command...
[22:10:58] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠏ Processing command...
[22:10:58] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠏ Processing command...
[22:10:58] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠋ Processing command...
[22:10:58] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠋ Processing command...
[22:10:58] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[22:10:58] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[22:10:58] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[22:10:58] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[22:10:58] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[22:10:58] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.54s
⠼ Processing command...
[22:10:58] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠼ Processing command...
[22:10:58] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:10:58] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠼ Processing command...
[22:10:58] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:10:58] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠴ Processing command...
[22:10:58] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:10:58] [STDOUT] 
⏳ Still in loading screens, waiting...
⠴ Processing command...
[22:10:58] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:10:59] [STDOUT] 
⏳ Elapsed: 14.7s / 20s
⠇ Processing command...
[22:10:59] [RICH_CONSOLE] ⏳ Elapsed: 14.7s / 20s
[22:10:59] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠋ Processing command...
[22:10:59] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:10:59] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠋ Processing command...
[22:10:59] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:10:59] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠋ Processing command...
[22:10:59] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠋ Processing command...
[22:10:59] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠋ Processing command...
[22:11:00] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[22:11:00] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠙ Processing command...
[22:11:00] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[22:11:00] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠙ Processing command...
[22:11:00] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠙ Processing command...
[22:11:00] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠙ Processing command...
[22:11:00] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠙ Processing command...
[22:11:00] [STDOUT] 
✓ Classified 10 elements instantly
⠙ Processing command...
[22:11:00] [STDOUT] 
✓ Headless discovery found 10 elements
⠙ Processing command...
[22:11:00] [STDOUT] 
✅ Headless discovery: 10 elements
⠙ Processing command...
[22:11:00] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠙ Processing command...
[22:11:00] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠙ Processing command...
[22:11:00] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠹ Processing command...
[22:11:00] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠹ Processing command...
[22:11:00] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠹ Processing command...
[22:11:00] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠹ Processing command...
[22:11:00] [STDOUT] 
🎯 Performing targeted visual scan...
⠹ Processing command...
[22:11:00] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[22:11:00] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[22:11:00] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.59s
⠧ Processing command...
[22:11:00] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠧ Processing command...
[22:11:00] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:11:00] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠧ Processing command...
[22:11:00] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:11:00] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠇ Processing command...
[22:11:00] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:11:00] [STDOUT] 
⏳ Still in loading screens, waiting...
⠇ Processing command...
[22:11:00] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:11:01] [STDOUT] 
⏳ Elapsed: 16.6s / 20s
⠙ Processing command...
[22:11:01] [RICH_CONSOLE] ⏳ Elapsed: 16.6s / 20s
[22:11:01] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[22:11:01] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:11:01] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠸ Processing command...
[22:11:01] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:11:01] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠸ Processing command...
[22:11:01] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠸ Processing command...
[22:11:01] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠸ Processing command...
[22:11:01] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:01] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:01] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:01] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:01] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:01] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:01] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠸ Processing command...
[22:11:01] [STDOUT] 
✓ Classified 10 elements instantly
⠸ Processing command...
[22:11:01] [STDOUT] 
✓ Headless discovery found 10 elements
⠸ Processing command...
[22:11:01] [STDOUT] 
✅ Headless discovery: 10 elements
⠸ Processing command...
[22:11:01] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠸ Processing command...
[22:11:01] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠸ Processing command...
[22:11:01] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠸ Processing command...
[22:11:01] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠸ Processing command...
[22:11:01] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠸ Processing command...
[22:11:01] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠸ Processing command...
[22:11:01] [STDOUT] 
🎯 Performing targeted visual scan...
⠸ Processing command...
[22:11:02] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠧ Processing command...
[22:11:02] [STDOUT] 
✅ Visual validation: additional elements found
⠧ Processing command...
[22:11:02] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.36s
⠧ Processing command...
[22:11:02] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠧ Processing command...
[22:11:02] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:11:02] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠧ Processing command...
[22:11:02] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:11:02] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠇ Processing command...
[22:11:02] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:11:02] [STDOUT] 
⏳ Still in loading screens, waiting...
⠇ Processing command...
[22:11:02] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:11:03] [STDOUT] 
⏳ Elapsed: 18.2s / 20s
⠙ Processing command...
[22:11:03] [RICH_CONSOLE] ⏳ Elapsed: 18.2s / 20s
[22:11:03] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠹ Processing command...
[22:11:03] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:11:03] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠹ Processing command...
[22:11:03] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:11:03] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠹ Processing command...
[22:11:03] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠹ Processing command...
[22:11:03] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠹ Processing command...
[22:11:03] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:03] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:03] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠸ Processing command...
[22:11:03] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠼ Processing command...
[22:11:03] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠼ Processing command...
[22:11:03] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠼ Processing command...
[22:11:03] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠼ Processing command...
[22:11:03] [STDOUT] 
✓ Classified 10 elements instantly
⠼ Processing command...
[22:11:03] [STDOUT] 
✓ Headless discovery found 10 elements
⠼ Processing command...
[22:11:03] [STDOUT] 
✅ Headless discovery: 10 elements
⠼ Processing command...
[22:11:03] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠼ Processing command...
[22:11:03] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠼ Processing command...
[22:11:03] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠴ Processing command...
[22:11:03] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠴ Processing command...
[22:11:03] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠴ Processing command...
[22:11:03] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠴ Processing command...
[22:11:03] [STDOUT] 
🎯 Performing targeted visual scan...
⠴ Processing command...
[22:11:03] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠏ Processing command...
[22:11:03] [STDOUT] 
✅ Visual validation: additional elements found
⠏ Processing command...
[22:11:03] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.55s
⠏ Processing command...
[22:11:03] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠏ Processing command...
[22:11:03] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:11:03] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠏ Processing command...
[22:11:03] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:11:04] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠙ Processing command...
[22:11:04] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:11:04] [STDOUT] 
⏳ Still in loading screens, waiting...
⠙ Processing command...
[22:11:04] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:11:05] [STDOUT] 
⏳ Elapsed: 20.0s / 20s
⠸ Processing command...
[22:11:05] [RICH_CONSOLE] ⏳ Elapsed: 20.0s / 20s
[22:11:05] [STDOUT] 
✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
⠴ Processing command...
[22:11:05] [RICH_CONSOLE] ✅ Correct app is running: com.kemendikdasmen.rumahpendidikan
[22:11:05] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠴ Processing command...
[22:11:05] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:11:05] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠴ Processing command...
[22:11:05] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠴ Processing command...
[22:11:05] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠴ Processing command...
[22:11:05] [STDOUT] 
🚀 Found 0 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[22:11:05] [STDOUT] 
🚀 Found 15 elements using interactive strategy (unlimited mode - continuing)
⠦ Processing command...
[22:11:05] [STDOUT] 
🚀 Found 15 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[22:11:05] [STDOUT] 
🚀 Found 30 elements using content strategy (unlimited mode - continuing)
⠦ Processing command...
[22:11:05] [STDOUT] 
🚀 Found 36 elements using widgets strategy (unlimited mode - continuing)
⠦ Processing command...
[22:11:05] [STDOUT] 
🚀 Found 51 elements using fallback strategy (unlimited mode - continuing)
⠦ Processing command...
[22:11:05] [STDOUT] 
✓ Parsed 10 unique elements from XML
⠦ Processing command...
[22:11:05] [STDOUT] 
✓ Classified 10 elements instantly
⠦ Processing command...
[22:11:05] [STDOUT] 
✓ Headless discovery found 10 elements
⠦ Processing command...
[22:11:05] [STDOUT] 
✅ Headless discovery: 10 elements
⠦ Processing command...
[22:11:05] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠦ Processing command...
[22:11:05] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠦ Processing command...
[22:11:05] [STDOUT] 
✅ Deep hierarchy parsing found 15 elements
⠇ Processing command...
[22:11:05] [STDOUT] 
✅ Deep parsing: 15 additional elements
⠇ Processing command...
[22:11:05] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠇ Processing command...
[22:11:05] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠇ Processing command...
[22:11:05] [STDOUT] 
🎯 Performing targeted visual scan...
⠇ Processing command...
[22:11:05] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[22:11:05] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[22:11:05] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 10 total elements in 0.54s
⠹ Processing command...
[22:11:05] [STDOUT] 
✅ Hybrid Headless Detection found 10 elements
⠹ Processing command...
[22:11:05] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 10 elements
[22:11:05] [STDOUT] 
✅ UI elements accessible: 10 elements found
⠹ Processing command...
[22:11:05] [RICH_CONSOLE] ✅ UI elements accessible: 10 elements found
[22:11:05] [STDOUT] 
⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
⠸ Processing command...
[22:11:05] [RICH_CONSOLE] ⏳ App not ready: Readiness score: 2/4 - insufficient interactive elements (0 < 3); dynamic content detected (content indicators found: 
educational(1), navigation(2), interactive(3), general(2))
[22:11:05] [STDOUT] 
⏳ Still in loading screens, waiting...
⠸ Processing command...
[22:11:05] [RICH_CONSOLE] ⏳ Still in loading screens, waiting...
[22:11:06] [STDOUT] 
⏳ Elapsed: 21.8s / 20s
⠴ Processing command...
[22:11:06] [RICH_CONSOLE] ⏳ Elapsed: 21.8s / 20s
[22:11:06] [STDOUT] 
❌ App did not become ready within 20 seconds
⠴ Processing command...
[22:11:06] [RICH_CONSOLE] ❌ App did not become ready within 20 seconds
[22:11:06] [STDOUT] 
❌ App failed to become ready
⠴ Processing command...
[22:11:06] [RICH_CONSOLE] ❌ App failed to become ready
[22:11:06] [STDOUT] 
⚠️ WARNING: Failed to return to main page - attempting to continue
⠴ Processing command...
[22:11:06] [RICH_CONSOLE] ⚠️ WARNING: Failed to return to main page - attempting to continue
[22:11:06] [STDOUT] 
🎯 === ANALYZING FEATURE 2/10: 'Ruang Murid' (unknown) ===
⠴ Processing command...
[22:11:06] [RICH_CONSOLE] 
🎯 === ANALYZING FEATURE 2/10: 'Ruang Murid' (unknown) ===
[22:11:06] [STDOUT] 
📋 Strategy: Comprehensively analyze feature 'Ruang Murid' for maximum element discovery
⠴ Processing command...
[22:11:06] [RICH_CONSOLE] 📋 Strategy: Comprehensively analyze feature 'Ruang Murid' for maximum element discovery
[22:11:06] [STDOUT] 
🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
⠦ Processing command...
[22:11:06] [RICH_CONSOLE] 🧠 COMPREHENSIVE: AI will explore all sub-features and content areas
[22:11:06] [STDOUT] 
🏥 Pre-feature health check for 'Ruang Murid'...
⠦ Processing command...
[22:11:06] [RICH_CONSOLE] 🏥 Pre-feature health check for 'Ruang Murid'...
[22:11:06] [STDOUT] 
🏥 Checking application health...
⠦ Processing command...
[22:11:06] [RICH_CONSOLE] 🏥 Checking application health...
[22:11:07] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠇ Processing command...
[22:11:07] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:11:07] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠇ Processing command...
[22:11:07] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠇ Processing command...
[22:11:07] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠇ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 1 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 18 elements using interactive strategy (unlimited mode - continuing)
⠏ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 18 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 35 elements using content strategy (unlimited mode - continuing)
⠏ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 42 elements using widgets strategy (unlimited mode - continuing)
⠋ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 59 elements using fallback strategy (unlimited mode - continuing)
⠋ Processing command...
[22:11:07] [STDOUT] 
✓ Parsed 12 unique elements from XML
⠋ Processing command...
[22:11:07] [STDOUT] 
✓ Classified 12 elements instantly
⠋ Processing command...
[22:11:07] [STDOUT] 
✓ Headless discovery found 12 elements
⠋ Processing command...
[22:11:07] [STDOUT] 
✅ Headless discovery: 12 elements
⠋ Processing command...
[22:11:07] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠋ Processing command...
[22:11:07] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠋ Processing command...
[22:11:07] [STDOUT] 
✅ Deep hierarchy parsing found 17 elements
⠋ Processing command...
[22:11:07] [STDOUT] 
✅ Deep parsing: 17 additional elements
⠋ Processing command...
[22:11:07] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠋ Processing command...
[22:11:07] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠋ Processing command...
[22:11:07] [STDOUT] 
🎯 Performing targeted visual scan...
⠋ Processing command...
[22:11:07] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠼ Processing command...
[22:11:07] [STDOUT] 
✅ Visual validation: additional elements found
⠼ Processing command...
[22:11:07] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 12 total elements in 0.44s
⠼ Processing command...
[22:11:07] [STDOUT] 
✅ Hybrid Headless Detection found 12 elements
⠼ Processing command...
[22:11:07] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 12 elements
[22:11:07] [STDOUT] 
✅ UI elements accessible: 12 elements found
⠦ Processing command...
[22:11:07] [RICH_CONSOLE] ✅ UI elements accessible: 12 elements found
[22:11:07] [STDOUT] 
🚀 Using Professional Hybrid Headless Detection
⠧ Processing command...
[22:11:07] [RICH_CONSOLE] 🚀 Using Professional Hybrid Headless Detection
[22:11:07] [STDOUT] 
🚀 Starting COMPREHENSIVE Hybrid Detection for Maximum Element Discovery...
⠧ Processing command...
[22:11:07] [STDOUT] 
📋 Strategy 1: Enhanced headless bulk discovery
⠧ Processing command...
[22:11:07] [STDOUT] 
⚡ Phase 1: Headless Bulk Discovery
⠧ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 1 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 18 elements using interactive strategy (unlimited mode - continuing)
⠧ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 18 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 35 elements using content strategy (unlimited mode - continuing)
⠧ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 42 elements using widgets strategy (unlimited mode - continuing)
⠧ Processing command...
[22:11:07] [STDOUT] 
🚀 Found 59 elements using fallback strategy (unlimited mode - continuing)
⠧ Processing command...
[22:11:07] [STDOUT] 
✓ Parsed 12 unique elements from XML
⠧ Processing command...
[22:11:07] [STDOUT] 
✓ Classified 12 elements instantly
⠧ Processing command...
[22:11:07] [STDOUT] 
✓ Headless discovery found 12 elements
⠧ Processing command...
[22:11:07] [STDOUT] 
✅ Headless discovery: 12 elements
⠧ Processing command...
[22:11:07] [STDOUT] 
📋 Strategy 2: Deep hierarchy parsing
⠧ Processing command...
[22:11:07] [STDOUT] 
🔍 Deep hierarchy parsing for maximum element discovery
⠧ Processing command...
[22:11:07] [STDOUT] 
✅ Deep hierarchy parsing found 17 elements
⠧ Processing command...
[22:11:07] [STDOUT] 
✅ Deep parsing: 17 additional elements
⠧ Processing command...
[22:11:07] [STDOUT] 
📋 Strategy 3: Visual validation for comprehensive coverage
⠧ Processing command...
[22:11:07] [STDOUT] 
🔍 Phase 2: Smart Visual Validation
⠧ Processing command...
[22:11:07] [STDOUT] 
🎯 Performing targeted visual scan...
⠧ Processing command...
[22:11:08] [STDOUT] 
✓ Targeted scan found 0 additional elements
⠹ Processing command...
[22:11:08] [STDOUT] 
✅ Visual validation: additional elements found
⠹ Processing command...
[22:11:08] [STDOUT] 
🎯 COMPREHENSIVE DETECTION COMPLETE: 12 total elements in 0.36s
⠹ Processing command...
[22:11:08] [STDOUT] 
✅ Hybrid Headless Detection found 12 elements
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] ✅ Hybrid Headless Detection found 12 elements
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): button (confidence: 0.60)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): button (confidence: 0.60)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): navigation (confidence: 0.40)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:11:08] [STDOUT] 
🎯 Enhanced classification (low confidence): input (confidence: 0.50)
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Enhanced classification (low confidence): input (confidence: 0.50)
[22:11:08] [STDOUT] 
✅ App is responsive
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] ✅ App is responsive
[22:11:08] [STDOUT] 
📊 Feature Ruang Murid started:
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 📊 Feature Ruang Murid started:
[22:11:08] [STDOUT] 
   Elements: 0
⠹ Processing command...
[22:11:08] [RICH_CONSOLE]    Elements: 0
[22:11:08] [STDOUT] 
   Interactions: 0
⠹ Processing command...
[22:11:08] [RICH_CONSOLE]    Interactions: 0
[22:11:08] [STDOUT] 
   Sub-features: 0
⠹ Processing command...
[22:11:08] [RICH_CONSOLE]    Sub-features: 0
[22:11:08] [STDOUT] 
🔧 Problem solver updated: 45 elements, 0 unknown, 2.6min
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🔧 Problem solver updated: 45 elements, 0 unknown, 2.6min
[22:11:08] [STDOUT] 
🎯 Using COMPREHENSIVE feature analysis for 'Ruang Murid'
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 Using COMPREHENSIVE feature analysis for 'Ruang Murid'
[22:11:08] [STDOUT] 
🎯 ACTUALLY ENTERING Feature 2/10: 'Ruang Murid'
⠹ Processing command...
[22:11:08] [RICH_CONSOLE] 🎯 ACTUALLY ENTERING Feature 2/10: 'Ruang Murid'
[22:11:08] [STDOUT] 
📸 Pre-click screen captured for verification
⠴ Processing command...
[22:11:08] [RICH_CONSOLE] 📸 Pre-click screen captured for verification
[22:11:08] [STDOUT] 
📝 Tracked main app interaction: 
⠦ Processing command...
[22:11:08] [RICH_CONSOLE] 📝 Tracked main app interaction: 
[22:11:08] [STDOUT] 
🖱️ CLICKING on feature element: 'Ruang Murid'
⠦ Processing command...
[22:11:08] [RICH_CONSOLE] 🖱️ CLICKING on feature element: 'Ruang Murid'
[22:11:33] [STDOUT] 
🔍 Real-time problem monitoring stopped
⠦ Processing command...
[22:11:33] [ERROR] ❌ ERROR in Deep UI Analysis: "'code'"
[22:11:33] [ERROR] ❌ Error Type: Exception
[22:11:33] [ERROR] ❌ Stack Trace:
[22:11:33] [ERROR]     NoneType: None
[22:11:33] [ERROR] ❌ Additional Context:
[22:11:33] [ERROR]     error_message: "'code'"
[22:11:33] [ERROR]     analysis_duration: 180.54s
[22:11:33] [ERROR]     elements_found_before_failure: 45
[22:11:33] [ERROR]     step: deep_ui_analysis
[22:11:33] [INFO] 📱 AI Analysis - ui_analysis_failed: UI analysis failed: "'code'"
[22:11:33] [INFO] ============================================================
[22:11:33] [ERROR] ❌ AI Analysis Failed: "'code'"
[22:11:33] [INFO] End Time: 2025-06-20 22:11:33

================================================================================
Session End Time: 2025-06-20 22:11:33
Log File: logs/ai_analysis_20250620_220821_terminal.txt
================================================================================
