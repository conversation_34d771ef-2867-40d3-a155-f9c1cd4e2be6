{"timestamp": "2025-06-20T23:19:30.158262", "analysis_data": [{"timestamp": "2025-06-20T23:14:29.428860", "screen_name": "Application Screen", "feature_category": "App Section Feature_0_strategy1", "depth": 1, "element_index": 0, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 17)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "Feature_0_depth_1_element_0", "unique_identifier": "class:_bounds:[63,556][1377,895]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_App_Section_Feature_0_strategy1", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0}, {"timestamp": "2025-06-20T23:14:29.445284", "screen_name": "Application Screen", "feature_category": "App Section Feature_0_strategy1", "depth": 1, "element_index": 10, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 27)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "Feature_0_depth_1_element_10", "unique_identifier": "class:_bounds:[0,0][1440,3120]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_App_Section_Feature_0_strategy1", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0}, {"timestamp": "2025-06-20T23:14:29.449662", "screen_name": "Application Screen", "feature_category": "App Section Feature_0_strategy1", "depth": 1, "element_index": 13, "resource_id": "", "class_name": "", "element_type": "list", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 30)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": true, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["scroll", "focus", "select"], "element_function": "unknown_element", "navigation_path": "Feature_0_depth_1_element_13", "unique_identifier": "class:_bounds:[0,144][1440,2830]", "interaction_methods": ["scroll_up", "scroll_down", "scroll_left", "scroll_right", "fling", "swipe", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "scroll", "scrollTo", "dragAndDropToObject", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "list_App_Section_Feature_0_strategy1", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0}, {"timestamp": "2025-06-20T23:14:29.452049", "screen_name": "Application Screen", "feature_category": "App Section Feature_0_strategy1", "depth": 1, "element_index": 14, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 31)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "Feature_0_depth_1_element_14", "unique_identifier": "class:_bounds:[63,412][924,486]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_App_Section_Feature_0_strategy1", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0}, {"timestamp": "2025-06-20T23:14:29.456760", "screen_name": "Application Screen", "feature_category": "App Section Feature_0_strategy1", "depth": 1, "element_index": 16, "resource_id": "", "class_name": "android.widget.Button", "element_type": "button", "text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "unique_description": "Documents and Resources", "package": "com.kemendikdasmen.rumahpendidikan", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*[@content-desc='Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja']", "css_selector": ".<PERSON><PERSON>", "id_selector": "", "text_selector": "", "accessibility_id": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "class_selector": "<PERSON><PERSON>", "capabilities": ["click", "focus", "long_click", "double_click", "select", "verify_text", "get_text_content"], "element_function": "action_button", "navigation_path": "Feature_0_depth_1_element_16", "unique_identifier": "desc:<PERSON><PERSON><PERSON><PERSON>men rujukan untuk <PERSON>o", "interaction_methods": ["click", "long_click", "double_click", "is_enabled", "get_text", "verify_text", "get_text_content", "verify_content_description", "get_bounds", "get_center", "is_displayed", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "verifyText", "assertText", "verifyElementPresent", "assertElementPresent", "storeText", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight", "store", "storeAttribute", "storeEval", "storeTitle", "storeXpathCount"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "<PERSON><PERSON><PERSON><PERSON>...", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "enhanced_extraction": true, "extraction_method": "enhanced_for_missing_elements", "is_potential_missing_element": true}, {"timestamp": "2025-06-20T23:16:10.101412", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 0, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 94)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_0", "unique_identifier": "class:_bounds:[67,1628][383,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.107671", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 1, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 95)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_1", "unique_identifier": "class:_bounds:[397,1628][713,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.110895", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 2, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 96)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_2", "unique_identifier": "class:_bounds:[727,1628][1043,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.112916", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 3, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 97)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_3", "unique_identifier": "class:_bounds:[1057,1628][1374,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.114526", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 4, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 98)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_4", "unique_identifier": "class:_bounds:[67,2083][383,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.116510", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 5, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 99)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_5", "unique_identifier": "class:_bounds:[397,2083][713,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.118339", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 6, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 100)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_6", "unique_identifier": "class:_bounds:[727,2083][1043,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.120407", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 7, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 101)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_7", "unique_identifier": "class:_bounds:[1057,2083][1374,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.123223", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 8, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 102)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": true, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_8", "unique_identifier": "class:_bounds:[0,2830][360,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.125202", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 9, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 103)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_9", "unique_identifier": "class:_bounds:[360,2830][720,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.127027", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 10, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 104)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_10", "unique_identifier": "class:_bounds:[720,2830][1080,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.128689", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 11, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 105)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_11", "unique_identifier": "class:_bounds:[1080,2830][1440,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.130344", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 12, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 106)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_12", "unique_identifier": "class:_bounds:[0,0][1440,3120]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.132357", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 13, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 107)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_13", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.134037", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 14, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 108)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_14", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.135708", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 15, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 109)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_15", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.137563", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 16, "resource_id": "", "class_name": "", "element_type": "list", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 110)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": true, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["scroll", "focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_16", "unique_identifier": "class:_bounds:[0,144][1440,2830]", "interaction_methods": ["scroll_up", "scroll_down", "scroll_left", "scroll_right", "fling", "swipe", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "scroll", "scrollTo", "dragAndDropToObject", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "list_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.139820", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 17, "resource_id": "", "class_name": "", "element_type": "element", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 111)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_17", "unique_identifier": "class:_bounds:[63,214][510,337]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "element_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.141429", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 18, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 112)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_18", "unique_identifier": "class:_bounds:[63,498][1211,876]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.143119", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 19, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 113)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_19", "unique_identifier": "class:_bounds:[67,1450][983,1523]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:16:10.144800", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 20, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 114)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_20", "unique_identifier": "class:_bounds:[63,2696][924,2769]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:17:58.067089", "screen_name": "Application Screen", "feature_category": "School Management", "depth": 1, "element_index": 21, "resource_id": "", "class_name": "android.widget.ImageView", "element_type": "button", "text": "", "content_desc": "<PERSON><PERSON>", "unique_description": "Access School Management Portal", "package": "com.kemendikdasmen.rumahpendidikan", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*[@content-desc='<PERSON><PERSON>']", "css_selector": ".ImageView", "id_selector": "", "text_selector": "", "accessibility_id": "<PERSON><PERSON>", "class_selector": "ImageView", "capabilities": ["click", "focus", "verify_image", "get_image_content", "verify_image_properties", "select", "verify_text", "get_text_content", "long_click", "double_click"], "element_function": "image_display", "navigation_path": "Feature_1_depth_1_element_21", "unique_identifier": "desc:<PERSON><PERSON>", "interaction_methods": ["click", "long_click", "double_click", "get_image_content", "verify_image", "get_image_properties", "save_image", "compare_image", "verify_text", "get_text_content", "verify_content_description", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "verifyText", "assertText", "verifyElementPresent", "assertElementPresent", "storeText", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight", "store", "storeAttribute", "storeEval", "storeTitle", "storeXpathCount"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "<PERSON><PERSON>"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "<PERSON><PERSON>", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "enhanced_extraction": true, "extraction_method": "enhanced_for_missing_elements", "is_potential_missing_element": false}, {"timestamp": "2025-06-20T23:18:27.149994", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 0, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 218)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_0", "unique_identifier": "class:_bounds:[67,1628][383,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.156022", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 1, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 219)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_1", "unique_identifier": "class:_bounds:[397,1628][713,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.158486", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 2, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 220)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_2", "unique_identifier": "class:_bounds:[727,1628][1043,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.160025", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 3, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 221)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_3", "unique_identifier": "class:_bounds:[1057,1628][1374,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.161730", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 4, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 222)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_4", "unique_identifier": "class:_bounds:[67,2083][383,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.163317", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 5, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 223)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_5", "unique_identifier": "class:_bounds:[397,2083][713,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.164799", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 6, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 224)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_6", "unique_identifier": "class:_bounds:[727,2083][1043,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.166490", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 7, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 225)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_7", "unique_identifier": "class:_bounds:[1057,2083][1374,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.168119", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 8, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 226)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": true, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_8", "unique_identifier": "class:_bounds:[0,2830][360,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.169591", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 9, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 227)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_9", "unique_identifier": "class:_bounds:[360,2830][720,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.171028", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 10, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 228)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_10", "unique_identifier": "class:_bounds:[720,2830][1080,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.172749", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 11, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 229)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_11", "unique_identifier": "class:_bounds:[1080,2830][1440,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.174719", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 12, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 230)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_12", "unique_identifier": "class:_bounds:[0,0][1440,3120]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.176408", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 13, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 231)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_13", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.178062", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 14, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 232)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_14", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.179543", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 15, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 233)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_15", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.180935", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 16, "resource_id": "", "class_name": "", "element_type": "list", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 234)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": true, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["scroll", "focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_16", "unique_identifier": "class:_bounds:[0,144][1440,2830]", "interaction_methods": ["scroll_up", "scroll_down", "scroll_left", "scroll_right", "fling", "swipe", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "scroll", "scrollTo", "dragAndDropToObject", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "list_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.182564", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 17, "resource_id": "", "class_name": "", "element_type": "element", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 235)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_17", "unique_identifier": "class:_bounds:[63,214][510,337]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "element_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.184044", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 18, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 236)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_18", "unique_identifier": "class:_bounds:[63,498][1211,876]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.185499", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 19, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 237)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_19", "unique_identifier": "class:_bounds:[67,1450][983,1523]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:27.187075", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 20, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 238)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_20", "unique_identifier": "class:_bounds:[63,2696][924,2769]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.116399", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 0, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 312)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_0", "unique_identifier": "class:_bounds:[67,1628][383,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.120161", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 1, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 313)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_1", "unique_identifier": "class:_bounds:[397,1628][713,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.122091", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 2, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 314)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_2", "unique_identifier": "class:_bounds:[727,1628][1043,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.123726", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 3, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 315)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_3", "unique_identifier": "class:_bounds:[1057,1628][1374,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.125696", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 4, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 316)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_4", "unique_identifier": "class:_bounds:[67,2083][383,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.127464", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 5, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 317)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_5", "unique_identifier": "class:_bounds:[397,2083][713,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.128953", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 6, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 318)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_6", "unique_identifier": "class:_bounds:[727,2083][1043,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.130478", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 7, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 319)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_7", "unique_identifier": "class:_bounds:[1057,2083][1374,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.132122", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 8, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 320)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": true, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_8", "unique_identifier": "class:_bounds:[0,2830][360,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.133585", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 9, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 321)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_9", "unique_identifier": "class:_bounds:[360,2830][720,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.135048", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 10, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 322)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_10", "unique_identifier": "class:_bounds:[720,2830][1080,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.136601", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 11, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 323)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_11", "unique_identifier": "class:_bounds:[1080,2830][1440,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.138077", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 12, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 324)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_12", "unique_identifier": "class:_bounds:[0,0][1440,3120]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.139803", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 13, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 325)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_13", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.141892", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 14, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 326)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_14", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.143357", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 15, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 327)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_15", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.144715", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 16, "resource_id": "", "class_name": "", "element_type": "list", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 328)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": true, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["scroll", "focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_16", "unique_identifier": "class:_bounds:[0,144][1440,2830]", "interaction_methods": ["scroll_up", "scroll_down", "scroll_left", "scroll_right", "fling", "swipe", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "scroll", "scrollTo", "dragAndDropToObject", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "list_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.146144", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 17, "resource_id": "", "class_name": "", "element_type": "element", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 329)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_17", "unique_identifier": "class:_bounds:[63,214][510,337]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "element_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.147624", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 18, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 330)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_18", "unique_identifier": "class:_bounds:[63,498][1211,876]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.149040", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 19, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 331)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_19", "unique_identifier": "class:_bounds:[67,1450][983,1523]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:18:45.150455", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 20, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 332)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_20", "unique_identifier": "class:_bounds:[63,2696][924,2769]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.399334", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 0, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 406)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_0", "unique_identifier": "class:_bounds:[67,1628][383,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.404465", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 1, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 407)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_1", "unique_identifier": "class:_bounds:[397,1628][713,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.406634", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 2, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 408)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_2", "unique_identifier": "class:_bounds:[727,1628][1043,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.408648", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 3, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 409)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_3", "unique_identifier": "class:_bounds:[1057,1628][1374,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.410342", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 4, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 410)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_4", "unique_identifier": "class:_bounds:[67,2083][383,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.411923", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 5, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 411)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_5", "unique_identifier": "class:_bounds:[397,2083][713,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.413386", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 6, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 412)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_6", "unique_identifier": "class:_bounds:[727,2083][1043,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.414848", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 7, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 413)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_7", "unique_identifier": "class:_bounds:[1057,2083][1374,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.416477", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 8, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 414)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": true, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_8", "unique_identifier": "class:_bounds:[0,2830][360,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.417935", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 9, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 415)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_9", "unique_identifier": "class:_bounds:[360,2830][720,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.419384", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 10, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 416)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_10", "unique_identifier": "class:_bounds:[720,2830][1080,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.421063", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 11, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 417)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_11", "unique_identifier": "class:_bounds:[1080,2830][1440,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.422590", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 12, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 418)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_12", "unique_identifier": "class:_bounds:[0,0][1440,3120]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.424647", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 13, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 419)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_13", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.426354", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 14, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 420)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_14", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.427854", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 15, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 421)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_15", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.429239", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 16, "resource_id": "", "class_name": "", "element_type": "list", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 422)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": true, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["scroll", "focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_16", "unique_identifier": "class:_bounds:[0,144][1440,2830]", "interaction_methods": ["scroll_up", "scroll_down", "scroll_left", "scroll_right", "fling", "swipe", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "scroll", "scrollTo", "dragAndDropToObject", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "list_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.430827", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 17, "resource_id": "", "class_name": "", "element_type": "element", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 423)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_17", "unique_identifier": "class:_bounds:[63,214][510,337]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "element_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.432363", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 18, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 424)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_18", "unique_identifier": "class:_bounds:[63,498][1211,876]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.433797", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 19, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 425)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_19", "unique_identifier": "class:_bounds:[67,1450][983,1523]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:03.435323", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 20, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 426)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_20", "unique_identifier": "class:_bounds:[63,2696][924,2769]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.015712", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 0, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 500)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_0", "unique_identifier": "class:_bounds:[67,1628][383,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.022814", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 1, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 501)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_1", "unique_identifier": "class:_bounds:[397,1628][713,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.024962", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 2, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 502)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_2", "unique_identifier": "class:_bounds:[727,1628][1043,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.026807", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 3, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 503)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_3", "unique_identifier": "class:_bounds:[1057,1628][1374,1971]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.028580", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 4, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 504)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_4", "unique_identifier": "class:_bounds:[67,2083][383,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.030079", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 5, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 505)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_5", "unique_identifier": "class:_bounds:[397,2083][713,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.031538", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 6, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 506)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_6", "unique_identifier": "class:_bounds:[727,2083][1043,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.033034", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 7, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 507)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_7", "unique_identifier": "class:_bounds:[1057,2083][1374,2426]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.034542", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 8, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 508)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": true, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_8", "unique_identifier": "class:_bounds:[0,2830][360,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.035982", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 9, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 509)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_9", "unique_identifier": "class:_bounds:[360,2830][720,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.037413", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 10, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 510)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_10", "unique_identifier": "class:_bounds:[720,2830][1080,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.038908", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 11, "resource_id": "", "class_name": "", "element_type": "button", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 511)", "package": "", "clickable": true, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["click", "focus", "select", "long_click", "double_click"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_11", "unique_identifier": "class:_bounds:[1080,2830][1440,3036]", "interaction_methods": ["click", "long_click", "double_click", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["click", "clickAndWait", "clickAt", "select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "action", "automation_priority": "high", "Element_Name": "button_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.040737", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 12, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 512)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_12", "unique_identifier": "class:_bounds:[0,0][1440,3120]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.042728", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 13, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 513)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_13", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.044235", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 14, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 514)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_14", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.045690", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 15, "resource_id": "", "class_name": "", "element_type": "navigation", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 515)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_15", "unique_identifier": "class:_bounds:[0,0][1440,3036]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "navigation_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.047057", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 16, "resource_id": "", "class_name": "", "element_type": "list", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 516)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": true, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["scroll", "focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_16", "unique_identifier": "class:_bounds:[0,144][1440,2830]", "interaction_methods": ["scroll_up", "scroll_down", "scroll_left", "scroll_right", "fling", "swipe", "get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "scroll", "scrollTo", "dragAndDropToObject", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "list_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.048627", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 17, "resource_id": "", "class_name": "", "element_type": "element", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 517)", "package": "", "clickable": false, "enabled": true, "focusable": false, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_17", "unique_identifier": "class:_bounds:[63,214][510,337]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": false, "element_category": "unknown", "automation_priority": "low", "Element_Name": "element_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.050085", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 18, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 518)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_18", "unique_identifier": "class:_bounds:[63,498][1211,876]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.051517", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 19, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 519)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_19", "unique_identifier": "class:_bounds:[67,1450][983,1523]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}, {"timestamp": "2025-06-20T23:19:21.053181", "screen_name": "Main Application Screen", "feature_category": "Main Application", "depth": 0, "element_index": 20, "resource_id": "", "class_name": "", "element_type": "input", "text": "", "content_desc": "", "unique_description": "Interactive Element (Item 520)", "package": "", "clickable": false, "enabled": true, "focusable": true, "scrollable": false, "checkable": false, "checked": false, "selected": false, "xpath": "//*", "css_selector": "*", "id_selector": "", "text_selector": "", "accessibility_id": "", "class_selector": "", "capabilities": ["focus", "select"], "element_function": "unknown_element", "navigation_path": "main_page_depth_0_element_20", "unique_identifier": "class:_bounds:[63,2696][924,2769]", "interaction_methods": ["get_bounds", "get_center", "is_displayed", "is_enabled", "focus", "clear_focus", "is_focused", "select", "deselect", "is_selected"], "testing_commands": ["select", "selectAndWait", "storeSelectedValue", "waitForElementPresent", "waitForElementToLoad", "waitForVisible", "waitForPageToLoad", "captureScreenshot", "verify", "highlight"], "sample_test_values": {"test_value": "test", "empty_value": "", "verify_text": "element_text"}, "is_interactive": true, "element_category": "unknown", "automation_priority": "medium", "Element_Name": "input_Main_Application", "adaptive_name": "Unknown", "element_purpose": "unknown", "discovery_confidence": 0.0, "mobile_element_type": "unknown", "best_locator_strategy": "none", "best_locator_value": "", "locator_stability": "low", "automation_strategy": "basic_interaction", "interaction_capabilities": [], "cross_platform_compatible": false, "all_available_locators": {}, "mobile_best_practices": [], "automation_readiness": "low", "automation_score": 0, "cached": false}], "navigation_paths": [], "feature_map": {"features_completed": ["Feature_0", "Feature_1"], "current_feature": "Feature_4", "feature_progress": []}, "session_metadata": {"session_id": "20250620_231158", "app_package": "com.kemendikdasmen.rumahpendidikan", "total_elements": 111, "analysis_type": "persistent_analysis", "elements_new_found": 104, "elements_already_found": 0, "cache_hit_rate": 0, "deduplication_rate": 0.0}}