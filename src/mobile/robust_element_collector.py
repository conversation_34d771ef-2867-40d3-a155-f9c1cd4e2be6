"""
Robust Element Collector - Handles unstable apps with checkpoint-based progressive collection
Designed to work around app crashes and stability issues while maintaining quality
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from rich.console import Console
import logging

from storage.persistent_excel_manager import PersistentExcelManager

console = Console()
logger = logging.getLogger(__name__)


class RobustElementCollector:
    """
    Robust element collector that handles unstable apps through:
    1. Checkpoint-based progressive collection
    2. Multiple collection strategies
    3. Crash recovery with state preservation
    4. Quality-first approach with fallback mechanisms
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.checkpoints = []
        self.collected_elements = {}
        self.collection_state = {
            "current_section": None,
            "sections_completed": [],
            "elements_found": 0,
            "last_checkpoint": None,
            "recovery_count": 0
        }
        
        # Initialize managers
        self.persistent_excel_manager = None
        
        # Collection strategies (ordered by robustness)
        self.collection_strategies = [
            self._strategy_screenshot_based_collection,
            self._strategy_xml_hierarchy_collection,
            self._strategy_incremental_element_collection,
            self._strategy_emergency_collection
        ]
        
        # Menu structure mapping for <PERSON><PERSON><PERSON>
        self.menu_structure = self._initialize_menu_structure()
        
    def _initialize_menu_structure(self) -> Dict[str, Any]:
        """Initialize the expected menu structure for systematic collection"""
        return {
            "main_sections": [
                {
                    "name": "Beranda",
                    "type": "main_page",
                    "elements": ["Rumah Pendidikan", "Jelajahi Beragam Layanan", "Search"]
                },
                {
                    "name": "Ruang GTK",
                    "type": "menu_section",
                    "sub_items": [
                        "Diklat", "Sertifikat Pendidik", "Pelatihan Mandiri", "Komunitas",
                        "Pengelolaan Kinerja", "Seleksi kepala sekolah", "Refleksi kompetensi",
                        "Perangkat ajar", "cp/atp", "Ide praktik", "Bukti karya", "video inspirasi",
                        "Asesmen", "Kelas", "Pengelolaan Pembelajaran", "Peningkatan kompetensi",
                        "Pengelolaan satuan pendidikan"
                    ]
                },
                {
                    "name": "Ruang Murid",
                    "type": "menu_section",
                    "sub_items": [
                        "Sumber belajar", "Buku bacaan digital", "7 kebiasaan anak indonesia hebat",
                        "Album lagu anak", "Bank soal", "Riwayat pendidikan", "Akun pendidikan"
                    ]
                },
                {
                    "name": "Ruang sekolah",
                    "type": "menu_section",
                    "sub_items": [
                        "Profil sekolah", "Rapor satuan pendidikan", "Rencana kegiatan dan belanja sekolah",
                        "Bantuan operasional", "Akun pendidikan"
                    ]
                },
                {
                    "name": "Ruang bahasa",
                    "type": "menu_section",
                    "sub_items": [
                        "Kamus bahasa", "Penerjemahan daring", "Layanan UKBI", "BIPA Daring"
                    ]
                },
                {
                    "name": "Ruang pemerintah",
                    "type": "menu_section",
                    "sub_items": [
                        "Neraca pendidikan daerah", "Rapor pendidikan daerah", "Akun pendidikan"
                    ]
                },
                {
                    "name": "Ruang mitra",
                    "type": "menu_section",
                    "sub_items": [
                        "Publikasi ilmiah", "Mitra barjas pendidikan", "Kemitraan pendidikan", "Relawan pendidikan"
                    ]
                },
                {
                    "name": "Ruang Publik",
                    "type": "menu_section",
                    "sub_items": [
                        "Pusat Perbukaan", "Majalah Pendidikan", "Bantuan Pendidikan",
                        "Kursus Digital", "Layanan Informasi dan Pengaduan", "Informasi Data Pendidikan"
                    ]
                },
                {
                    "name": "Ruang Orang Tua",
                    "type": "menu_section",
                    "sub_items": [
                        "Layanan Informasi dan Pengaduan", "Pengaduan Pendampingan", "Konsultasi Pendidikan"
                    ]
                }
            ],
            "navigation_elements": [
                "Beranda", "Cari", "Pemberitahuan", "Akun", "Pusat Bantuan",
                "Tentang Aplikasi", "Kebijakan Privasi", "Syarat dan Ketentuan", "Keluar"
            ],
            "special_sections": [
                "Layanan Paling banyak diakses", "Lihat Semua", "Butuh Bantuan"
            ]
        }
    
    async def collect_all_elements_robustly(self, device, app_package: str, 
                                          persistent_excel_manager: PersistentExcelManager) -> Dict[str, Any]:
        """
        Main method to collect all elements robustly despite app instability
        """
        try:
            console.print("[cyan]🛡️ ROBUST ELEMENT COLLECTION STARTED[/cyan]")
            console.print("[cyan]📋 Strategy: Checkpoint-based progressive collection with crash recovery[/cyan]")
            
            self.persistent_excel_manager = persistent_excel_manager
            
            # Initialize collection state
            await self._initialize_collection_state(app_package)
            
            # Start progressive collection
            collection_result = await self._progressive_collection_with_checkpoints(device, app_package)
            
            # Final verification and quality check
            final_result = await self._verify_collection_completeness(collection_result)
            
            console.print(f"[green]✅ ROBUST COLLECTION COMPLETED: {final_result['total_elements']} elements[/green]")
            return final_result
            
        except Exception as e:
            logger.error(f"Robust element collection failed: {e}")
            return await self._emergency_collection_fallback(device, app_package)
    
    async def _initialize_collection_state(self, app_package: str):
        """Initialize collection state and load any existing progress"""
        try:
            console.print("[cyan]📋 Initializing robust collection state...[/cyan]")
            
            # Load existing elements from persistent storage
            if self.persistent_excel_manager:
                existing_count = len(self.persistent_excel_manager.existing_elements)
                console.print(f"[cyan]📊 Found {existing_count} existing elements in persistent storage[/cyan]")
                
            # Initialize checkpoint system
            self.collection_state = {
                "app_package": app_package,
                "start_time": datetime.now().isoformat(),
                "current_section": None,
                "sections_completed": [],
                "elements_found": existing_count if self.persistent_excel_manager else 0,
                "last_checkpoint": None,
                "recovery_count": 0,
                "collection_strategy": "progressive_with_checkpoints"
            }
            
            console.print("[green]✅ Collection state initialized[/green]")
            
        except Exception as e:
            logger.error(f"Failed to initialize collection state: {e}")
            raise
    
    async def _progressive_collection_with_checkpoints(self, device, app_package: str) -> Dict[str, Any]:
        """
        Progressive collection with checkpoint-based recovery
        """
        try:
            console.print("[cyan]🎯 Starting progressive collection with checkpoints...[/cyan]")
            
            total_elements_collected = 0
            sections_completed = 0
            
            # Process each main section systematically
            for section_index, section in enumerate(self.menu_structure["main_sections"]):
                try:
                    console.print(f"[yellow]📋 SECTION {section_index + 1}/{len(self.menu_structure['main_sections'])}: {section['name']}[/yellow]")
                    
                    # Create checkpoint before section
                    checkpoint = await self._create_checkpoint(device, section, section_index)
                    
                    # Collect elements from this section
                    section_result = await self._collect_section_elements_robustly(device, section, app_package)
                    
                    if section_result["success"]:
                        total_elements_collected += section_result["elements_collected"]
                        sections_completed += 1
                        self.collection_state["sections_completed"].append(section["name"])
                        
                        console.print(f"[green]✅ Section '{section['name']}' completed: {section_result['elements_collected']} elements[/green]")
                    else:
                        console.print(f"[yellow]⚠️ Section '{section['name']}' partially completed: {section_result.get('elements_collected', 0)} elements[/yellow]")
                        
                        # Try recovery if section failed
                        recovery_result = await self._attempt_section_recovery(device, section, checkpoint)
                        if recovery_result["success"]:
                            total_elements_collected += recovery_result["elements_collected"]
                            sections_completed += 1
                    
                    # Save progress after each section
                    await self._save_progress_checkpoint(total_elements_collected, sections_completed)
                    
                except Exception as e:
                    logger.warning(f"Section {section['name']} failed: {e}")
                    # Continue with next section
                    continue
            
            # Collect navigation elements
            nav_result = await self._collect_navigation_elements_robustly(device, app_package)
            total_elements_collected += nav_result.get("elements_collected", 0)
            
            return {
                "success": True,
                "total_elements": total_elements_collected,
                "sections_completed": sections_completed,
                "collection_strategy": "progressive_with_checkpoints",
                "recovery_count": self.collection_state["recovery_count"]
            }
            
        except Exception as e:
            logger.error(f"Progressive collection failed: {e}")
            return await self._emergency_collection_fallback(device, app_package)

    async def _collect_section_elements_robustly(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Robustly collect elements from a specific section with multiple strategies
        """
        try:
            section_name = section["name"]
            console.print(f"[cyan]🔍 Collecting elements from section: {section_name}[/cyan]")

            elements_collected = 0
            strategies_tried = 0

            # Try each collection strategy until successful
            for strategy_index, strategy in enumerate(self.collection_strategies):
                try:
                    console.print(f"[cyan]📊 Strategy {strategy_index + 1}/{len(self.collection_strategies)}: {strategy.__name__}[/cyan]")

                    strategy_result = await strategy(device, section, app_package)
                    strategies_tried += 1

                    if strategy_result["success"] and strategy_result["elements_collected"] > 0:
                        elements_collected = strategy_result["elements_collected"]
                        console.print(f"[green]✅ Strategy successful: {elements_collected} elements collected[/green]")
                        break
                    else:
                        console.print(f"[yellow]⚠️ Strategy {strategy.__name__} found {strategy_result.get('elements_collected', 0)} elements[/yellow]")

                except Exception as e:
                    logger.warning(f"Collection strategy {strategy.__name__} failed: {e}")
                    continue

            return {
                "success": elements_collected > 0,
                "elements_collected": elements_collected,
                "strategies_tried": strategies_tried,
                "section_name": section_name
            }

        except Exception as e:
            logger.error(f"Section collection failed for {section.get('name', 'Unknown')}: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    def _generate_element_key(self, element: Dict[str, Any]) -> str:
        """
        Generate a unique key for an element to avoid duplicates
        """
        try:
            # Create a unique key based on element properties
            key_parts = []

            # Use resource ID if available (most reliable)
            if element.get("resource_id"):
                key_parts.append(f"id:{element['resource_id']}")

            # Use text content if available
            if element.get("text") and element["text"].strip():
                key_parts.append(f"text:{element['text'].strip()}")

            # Use content description if available
            if element.get("content_desc") and element["content_desc"].strip():
                key_parts.append(f"desc:{element['content_desc'].strip()}")

            # Use class name
            if element.get("class_name"):
                key_parts.append(f"class:{element['class_name']}")

            # Use bounds as fallback
            if element.get("bounds"):
                key_parts.append(f"bounds:{element['bounds']}")

            # If no identifying features, use a hash of all properties
            if not key_parts:
                key_parts.append(f"hash:{hash(str(sorted(element.items())))}")

            return "|".join(key_parts)

        except Exception as e:
            logger.debug(f"Element key generation failed: {e}")
            return f"fallback:{hash(str(element))}"

    async def _strategy_screenshot_based_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 1: Screenshot-based element collection (most robust for unstable apps)
        ENHANCED: Uses ALL selectors to ensure comprehensive element collection
        """
        try:
            console.print("[cyan]📸 Using comprehensive screenshot-based collection strategy[/cyan]")

            total_elements_found = 0
            unique_elements = set()  # Track unique elements to avoid duplicates
            selector_results = {}

            try:
                # ENHANCED: Comprehensive selector list for maximum element discovery
                selectors = [
                    "//*",  # All elements (most comprehensive)
                    "//*[@clickable='true']",  # All clickable elements
                    "//*[@enabled='true']",  # All enabled elements
                    "//*[string-length(@text)>0]",  # All elements with text
                    "//*[@resource-id]",  # All elements with resource IDs
                    "//*[@content-desc]",  # All elements with content descriptions
                    "//android.widget.*",  # All Android widgets
                    "//android.view.*",  # All Android views
                    "//*[@focusable='true']",  # All focusable elements
                    "//*[@scrollable='true']",  # All scrollable elements
                    "//*[@checkable='true']",  # All checkable elements
                    "//*[@selected='true']",  # All selected elements
                    "//*[@long-clickable='true']",  # All long-clickable elements
                ]

                console.print(f"[cyan]🎯 COMPREHENSIVE COLLECTION: Using {len(selectors)} selectors for maximum coverage[/cyan]")

                # CRITICAL FIX: Use ALL selectors instead of breaking after first success
                for selector_index, selector in enumerate(selectors):
                    try:
                        console.print(f"[cyan]📊 Selector {selector_index + 1}/{len(selectors)}: {selector}[/cyan]")
                        elements = device.xpath(selector).all()

                        if elements:
                            # Process and save elements from this selector
                            processed_elements = await self._process_elements_for_section(elements, section, app_package)

                            # Track unique elements to avoid counting duplicates
                            new_elements = 0
                            for element in processed_elements:
                                element_key = self._generate_element_key(element)
                                if element_key not in unique_elements:
                                    unique_elements.add(element_key)
                                    new_elements += 1

                            selector_results[selector] = {
                                "total_found": len(processed_elements),
                                "new_unique": new_elements
                            }

                            total_elements_found += new_elements
                            console.print(f"[green]✅ Selector '{selector}' found {len(processed_elements)} elements ({new_elements} new unique)[/green]")
                        else:
                            console.print(f"[yellow]⚠️ Selector '{selector}' found 0 elements[/yellow]")
                            selector_results[selector] = {"total_found": 0, "new_unique": 0}

                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
                        selector_results[selector] = {"error": str(e)}
                        continue

                # Log comprehensive collection summary
                console.print(f"[bold green]🎯 COMPREHENSIVE COLLECTION COMPLETE: {total_elements_found} unique elements found[/bold green]")
                for selector, result in selector_results.items():
                    if "error" not in result:
                        console.print(f"[cyan]  • {selector}: {result['total_found']} total, {result['new_unique']} unique[/cyan]")

                return {
                    "success": total_elements_found > 0,
                    "elements_collected": total_elements_found,
                    "strategy": "comprehensive_screenshot_based",
                    "selector_results": selector_results,
                    "unique_elements_count": len(unique_elements)
                }

            except Exception as e:
                logger.warning(f"Screenshot analysis failed: {e}")
                return {"success": False, "elements_collected": 0, "error": str(e)}

        except Exception as e:
            logger.error(f"Screenshot-based collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_xml_hierarchy_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 2: XML hierarchy-based collection (good for stable moments)
        ENHANCED: Comprehensive XML parsing with multiple extraction methods
        """
        try:
            console.print("[cyan]🌳 Using comprehensive XML hierarchy collection strategy[/cyan]")

            total_elements_found = 0
            unique_elements = set()
            extraction_methods = []

            try:
                # Get XML hierarchy
                xml_hierarchy = device.dump_hierarchy()

                if xml_hierarchy:
                    console.print("[cyan]📋 XML hierarchy obtained, using multiple extraction methods[/cyan]")

                    # Method 1: Standard XML parsing
                    try:
                        elements_method1 = await self._parse_xml_hierarchy(xml_hierarchy, section)
                        if elements_method1:
                            processed_elements = await self._process_elements_for_section(elements_method1, section, app_package)
                            new_elements = 0
                            for element in processed_elements:
                                element_key = self._generate_element_key(element)
                                if element_key not in unique_elements:
                                    unique_elements.add(element_key)
                                    new_elements += 1
                            total_elements_found += new_elements
                            extraction_methods.append(f"Standard XML: {len(processed_elements)} total, {new_elements} unique")
                            console.print(f"[green]✅ Standard XML parsing found {len(processed_elements)} elements ({new_elements} unique)[/green]")
                    except Exception as e:
                        logger.debug(f"Standard XML parsing failed: {e}")

                    # Method 2: Comprehensive XML node extraction
                    try:
                        elements_method2 = await self._extract_all_xml_nodes(xml_hierarchy, section)
                        if elements_method2:
                            processed_elements = await self._process_elements_for_section(elements_method2, section, app_package)
                            new_elements = 0
                            for element in processed_elements:
                                element_key = self._generate_element_key(element)
                                if element_key not in unique_elements:
                                    unique_elements.add(element_key)
                                    new_elements += 1
                            total_elements_found += new_elements
                            extraction_methods.append(f"Comprehensive XML: {len(processed_elements)} total, {new_elements} unique")
                            console.print(f"[green]✅ Comprehensive XML extraction found {len(processed_elements)} elements ({new_elements} unique)[/green]")
                    except Exception as e:
                        logger.debug(f"Comprehensive XML extraction failed: {e}")

                console.print(f"[bold green]🌳 COMPREHENSIVE XML COLLECTION COMPLETE: {total_elements_found} unique elements[/bold green]")
                for method in extraction_methods:
                    console.print(f"[cyan]  • {method}[/cyan]")

                return {
                    "success": total_elements_found > 0,
                    "elements_collected": total_elements_found,
                    "strategy": "comprehensive_xml_hierarchy",
                    "extraction_methods": extraction_methods,
                    "unique_elements_count": len(unique_elements)
                }

            except Exception as e:
                logger.warning(f"XML hierarchy collection failed: {e}")
                return {"success": False, "elements_collected": 0, "error": str(e)}

        except Exception as e:
            logger.error(f"XML hierarchy collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_incremental_element_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 3: Incremental element collection with comprehensive scrolling and dynamic content discovery
        ENHANCED: Includes scrolling, interaction-based discovery, and dynamic content collection
        """
        try:
            console.print("[cyan]🔄 Using enhanced incremental collection strategy with scrolling[/cyan]")

            total_elements_found = 0
            unique_elements = set()
            max_attempts = 3
            collection_methods = []

            for attempt in range(max_attempts):
                try:
                    console.print(f"[cyan]📊 Enhanced incremental attempt {attempt + 1}/{max_attempts}[/cyan]")

                    # Method 1: Quick visible element collection
                    try:
                        elements = await asyncio.wait_for(
                            self._get_visible_elements_quickly(device),
                            timeout=10.0
                        )

                        if elements:
                            processed_elements = await self._process_elements_for_section(elements, section, app_package)
                            new_elements = 0
                            for element in processed_elements:
                                element_key = self._generate_element_key(element)
                                if element_key not in unique_elements:
                                    unique_elements.add(element_key)
                                    new_elements += 1
                            total_elements_found += new_elements
                            collection_methods.append(f"Quick visible: {len(processed_elements)} total, {new_elements} unique")
                            console.print(f"[green]✅ Quick visible collection found {len(processed_elements)} elements ({new_elements} unique)[/green]")
                    except Exception as e:
                        logger.debug(f"Quick visible collection failed: {e}")

                    # Method 2: Comprehensive scrolling collection
                    try:
                        scroll_result = await self._collect_elements_with_comprehensive_scrolling(device, section, app_package)
                        if scroll_result["success"]:
                            new_elements = 0
                            for element in scroll_result["elements"]:
                                element_key = self._generate_element_key(element)
                                if element_key not in unique_elements:
                                    unique_elements.add(element_key)
                                    new_elements += 1
                            total_elements_found += new_elements
                            collection_methods.append(f"Scrolling: {len(scroll_result['elements'])} total, {new_elements} unique")
                            console.print(f"[green]✅ Scrolling collection found {len(scroll_result['elements'])} elements ({new_elements} unique)[/green]")
                    except Exception as e:
                        logger.debug(f"Scrolling collection failed: {e}")

                    # Method 3: Dynamic content interaction
                    try:
                        dynamic_result = await self._collect_dynamic_content_elements(device, section, app_package)
                        if dynamic_result["success"]:
                            new_elements = 0
                            for element in dynamic_result["elements"]:
                                element_key = self._generate_element_key(element)
                                if element_key not in unique_elements:
                                    unique_elements.add(element_key)
                                    new_elements += 1
                            total_elements_found += new_elements
                            collection_methods.append(f"Dynamic content: {len(dynamic_result['elements'])} total, {new_elements} unique")
                            console.print(f"[green]✅ Dynamic content collection found {len(dynamic_result['elements'])} elements ({new_elements} unique)[/green]")
                    except Exception as e:
                        logger.debug(f"Dynamic content collection failed: {e}")

                    # If we found elements, break
                    if total_elements_found > 0:
                        break

                    # Small delay between attempts
                    await asyncio.sleep(2)

                except asyncio.TimeoutError:
                    console.print(f"[yellow]⏰ Enhanced incremental attempt {attempt + 1} timed out[/yellow]")
                    continue
                except Exception as e:
                    logger.warning(f"Enhanced incremental attempt {attempt + 1} failed: {e}")
                    continue

            console.print(f"[bold green]🔄 ENHANCED INCREMENTAL COLLECTION COMPLETE: {total_elements_found} unique elements[/bold green]")
            for method in collection_methods:
                console.print(f"[cyan]  • {method}[/cyan]")

            return {
                "success": total_elements_found > 0,
                "elements_collected": total_elements_found,
                "strategy": "enhanced_incremental_with_scrolling",
                "collection_methods": collection_methods,
                "unique_elements_count": len(unique_elements)
            }

        except Exception as e:
            logger.error(f"Enhanced incremental collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_emergency_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 4: Emergency collection (last resort, minimal but functional)
        """
        try:
            console.print("[red]🚨 Using emergency collection strategy[/red]")

            elements_found = 0

            try:
                # Use the most basic element detection
                basic_elements = []

                # Try basic XPath selectors
                emergency_selectors = ["//*[@bounds]", "//*"]

                for selector in emergency_selectors:
                    try:
                        elements = device.xpath(selector).all()
                        if elements:
                            # Take first 50 elements to avoid overwhelming
                            basic_elements = elements[:50]
                            break
                    except:
                        continue

                if basic_elements:
                    processed_elements = await self._process_elements_for_section(basic_elements, section, app_package)
                    elements_found = len(processed_elements)

                    console.print(f"[yellow]⚠️ Emergency collection found {elements_found} elements[/yellow]")

                return {
                    "success": elements_found > 0,
                    "elements_collected": elements_found,
                    "strategy": "emergency"
                }

            except Exception as e:
                logger.warning(f"Emergency collection failed: {e}")
                return {"success": False, "elements_collected": 0, "error": str(e)}

        except Exception as e:
            logger.error(f"Emergency collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _process_elements_for_section(self, elements: List, section: Dict, app_package: str) -> List[Dict]:
        """
        Process and filter elements for a specific section
        """
        try:
            processed_elements = []
            section_name = section.get("name", "Unknown")

            for element in elements:
                try:
                    # Extract element data
                    element_data = await self._extract_element_data(element)

                    if element_data and self._is_relevant_element(element_data, section):
                        # Add section context
                        element_data["section"] = section_name
                        element_data["app_package"] = app_package
                        element_data["collection_timestamp"] = datetime.now().isoformat()

                        # Save to persistent storage if available
                        if self.persistent_excel_manager:
                            try:
                                action_taken = self.persistent_excel_manager.add_or_update_element(element_data)
                                processed_elements.append(element_data)

                                if action_taken == 'new':
                                    console.print(f"[green]✅ NEW element: {element_data.get('text', 'No text')[:50]}[/green]")
                                elif action_taken == 'updated':
                                    console.print(f"[yellow]🔄 UPDATED element: {element_data.get('text', 'No text')[:50]}[/yellow]")
                                else:  # skipped
                                    console.print(f"[cyan]⏭️ EXISTING element: {element_data.get('text', 'No text')[:50]}[/cyan]")

                            except Exception as save_error:
                                console.print(f"[red]❌ Failed to save element: {save_error}[/red]")
                                # Still add to processed elements to avoid losing it
                                processed_elements.append(element_data)
                        else:
                            processed_elements.append(element_data)

                except Exception as e:
                    logger.debug(f"Failed to process element: {e}")
                    continue

            return processed_elements

        except Exception as e:
            logger.error(f"Element processing failed: {e}")
            return []

    async def _extract_element_data(self, element) -> Optional[Dict[str, Any]]:
        """
        Extract comprehensive data from a UI element
        """
        try:
            element_data = {}

            # Basic attributes
            try:
                element_data["text"] = getattr(element, 'text', '') or ''
                element_data["content_desc"] = getattr(element, 'content_desc', '') or ''
                element_data["resource_id"] = getattr(element, 'resource_id', '') or ''
                element_data["class_name"] = getattr(element, 'class_name', '') or ''
                element_data["package"] = getattr(element, 'package', '') or ''
                element_data["bounds"] = str(getattr(element, 'bounds', ''))
                element_data["clickable"] = getattr(element, 'clickable', False)
                element_data["enabled"] = getattr(element, 'enabled', False)
                element_data["focusable"] = getattr(element, 'focusable', False)
                element_data["scrollable"] = getattr(element, 'scrollable', False)
            except Exception as e:
                logger.debug(f"Failed to extract basic attributes: {e}")

            # Generate element signature for deduplication
            element_data["signature"] = self._generate_element_signature(element_data)

            return element_data if element_data.get("signature") else None

        except Exception as e:
            logger.debug(f"Element data extraction failed: {e}")
            return None

    def _generate_element_signature(self, element_data: Dict[str, Any]) -> str:
        """
        Generate a unique signature for element deduplication
        """
        try:
            # Create signature from key attributes
            text = element_data.get("text", "").strip()
            resource_id = element_data.get("resource_id", "").strip()
            class_name = element_data.get("class_name", "").strip()
            bounds = element_data.get("bounds", "").strip()

            signature_parts = [text, resource_id, class_name, bounds]
            signature = "|".join(signature_parts)

            return signature if signature.strip("|") else f"element_{hash(str(element_data))}"

        except Exception as e:
            logger.debug(f"Signature generation failed: {e}")
            return f"element_{hash(str(element_data))}"

    def _is_relevant_element(self, element_data: Dict[str, Any], section: Dict) -> bool:  # noqa: ARG002
        """
        Check if an element is relevant for the current section
        """
        try:
            text = element_data.get("text", "").lower()
            content_desc = element_data.get("content_desc", "").lower()
            resource_id = element_data.get("resource_id", "").lower()
            class_name = element_data.get("class_name", "").lower()
            bounds = element_data.get("bounds", "")

            # Always include clickable elements
            if element_data.get("clickable", False):
                return True

            # Include elements with meaningful text
            if text and len(text.strip()) > 0:
                return True

            # Include elements with content description
            if content_desc and len(content_desc.strip()) > 0:
                return True

            # Include elements with resource IDs
            if resource_id and len(resource_id.strip()) > 0:
                return True

            # Include elements with class names (more inclusive)
            if class_name and len(class_name.strip()) > 0:
                return True

            # Include elements with bounds (very inclusive - almost all UI elements have bounds)
            if bounds and len(bounds.strip()) > 0:
                return True

            # Include enabled elements
            if element_data.get("enabled", False):
                return True

            # Include focusable elements
            if element_data.get("focusable", False):
                return True

            # Include scrollable elements
            if element_data.get("scrollable", False):
                return True

            return False

        except Exception as e:
            logger.debug(f"Relevance check failed: {e}")
            return False

    async def _get_visible_elements_quickly(self, device) -> List:
        """
        Quickly get visible elements with minimal processing
        """
        try:
            # Use the fastest possible element detection
            elements = device.xpath("//*[@bounds]").all()
            return elements[:100]  # Limit to first 100 elements for speed

        except Exception as e:
            logger.debug(f"Quick element detection failed: {e}")
            return []

    async def _parse_xml_hierarchy(self, xml_hierarchy: str, section: Dict) -> List:  # noqa: ARG002
        """
        Parse XML hierarchy and extract elements
        """
        try:
            import xml.etree.ElementTree as ET

            root = ET.fromstring(xml_hierarchy)
            elements = []

            # Extract all nodes with useful attributes
            for node in root.iter():
                if node.attrib:
                    elements.append(node)

            return elements

        except Exception as e:
            logger.debug(f"XML parsing failed: {e}")
            return []

    async def _extract_all_xml_nodes(self, xml_hierarchy: str, section: Dict) -> List:  # noqa: ARG002
        """
        Comprehensive XML node extraction with multiple strategies
        """
        try:
            import xml.etree.ElementTree as ET

            root = ET.fromstring(xml_hierarchy)
            elements = []

            # Strategy 1: Extract all nodes with any attributes
            for node in root.iter():
                if node.attrib:
                    elements.append(node)

            # Strategy 2: Extract nodes with specific Android attributes
            android_attributes = [
                'text', 'content-desc', 'resource-id', 'class', 'package',
                'clickable', 'enabled', 'focusable', 'scrollable', 'bounds'
            ]

            for node in root.iter():
                for attr in android_attributes:
                    if attr in node.attrib and node.attrib[attr]:
                        if node not in elements:
                            elements.append(node)
                        break

            # Strategy 3: Extract all leaf nodes (nodes without children)
            for node in root.iter():
                if len(list(node)) == 0 and node.attrib:  # Leaf node with attributes
                    if node not in elements:
                        elements.append(node)

            return elements

        except Exception as e:
            logger.debug(f"Comprehensive XML extraction failed: {e}")
            return []

    async def _collect_elements_with_comprehensive_scrolling(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Comprehensive scrolling-based element collection to reveal hidden content
        """
        try:
            console.print("[cyan]📜 Starting comprehensive scrolling collection[/cyan]")

            all_elements = []
            unique_signatures = set()
            scroll_attempts = 0
            max_scroll_attempts = 5
            previous_screen_hashes = set()

            while scroll_attempts < max_scroll_attempts:
                try:
                    # Get current screen state
                    current_xml = device.dump_hierarchy()
                    current_hash = hash(current_xml)

                    # Check for infinite loop
                    if current_hash in previous_screen_hashes:
                        console.print("[yellow]⚠️ Screen state already seen - stopping scroll[/yellow]")
                        break
                    previous_screen_hashes.add(current_hash)

                    # Get current elements
                    current_elements = device.xpath("//*").all()

                    # Process current elements
                    for element in current_elements:
                        try:
                            element_data = await self._extract_element_data(element)
                            if element_data:
                                signature = self._generate_element_signature(element_data)
                                if signature not in unique_signatures:
                                    unique_signatures.add(signature)
                                    element_data["section"] = section.get("name", "Unknown")
                                    element_data["app_package"] = app_package
                                    element_data["collection_method"] = "comprehensive_scrolling"
                                    all_elements.append(element_data)
                        except Exception as e:
                            logger.debug(f"Failed to process scrolled element: {e}")
                            continue

                    # Perform scroll
                    try:
                        # Try scrollable element first
                        scrollable = device(scrollable=True)
                        if scrollable.exists():
                            scrollable.scroll.forward()
                            console.print(f"[green]✅ Scrolled using scrollable element (attempt {scroll_attempts + 1})[/green]")
                        else:
                            # Fallback to swipe
                            device.swipe_ext("down")
                            console.print(f"[green]✅ Scrolled using swipe (attempt {scroll_attempts + 1})[/green]")

                        await asyncio.sleep(1)

                        # Check if scroll changed content
                        new_xml = device.dump_hierarchy()
                        new_hash = hash(new_xml)

                        if current_hash == new_hash:
                            console.print("[yellow]⚠️ Scroll didn't change content - stopping[/yellow]")
                            break

                    except Exception as scroll_e:
                        logger.debug(f"Scroll attempt {scroll_attempts + 1} failed: {scroll_e}")
                        break

                    scroll_attempts += 1

                except Exception as e:
                    logger.debug(f"Scrolling iteration {scroll_attempts + 1} failed: {e}")
                    break

            console.print(f"[green]✅ Comprehensive scrolling complete: {len(all_elements)} elements collected[/green]")

            return {
                "success": len(all_elements) > 0,
                "elements": all_elements,
                "scroll_attempts": scroll_attempts,
                "unique_elements": len(unique_signatures)
            }

        except Exception as e:
            logger.error(f"Comprehensive scrolling collection failed: {e}")
            return {"success": False, "elements": [], "error": str(e)}

    async def _collect_dynamic_content_elements(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Collect elements that appear through interactions (dynamic content)
        """
        try:
            console.print("[cyan]🔄 Starting dynamic content collection[/cyan]")

            all_elements = []
            unique_signatures = set()
            interactions_performed = 0
            max_interactions = 10

            # Get initial clickable elements
            clickable_elements = device.xpath("//*[@clickable='true']").all()
            console.print(f"[cyan]🖱️ Found {len(clickable_elements)} clickable elements for interaction[/cyan]")

            for i, element in enumerate(clickable_elements[:max_interactions]):
                try:
                    # Get current screen state
                    pre_click_xml = device.dump_hierarchy()
                    pre_click_hash = hash(pre_click_xml)

                    # Click the element
                    element.click()
                    await asyncio.sleep(2)
                    interactions_performed += 1

                    # Check if screen changed
                    post_click_xml = device.dump_hierarchy()
                    post_click_hash = hash(post_click_xml)

                    if pre_click_hash != post_click_hash:
                        console.print(f"[green]✅ Interaction {i+1} revealed new content[/green]")

                        # Collect new elements
                        new_elements = device.xpath("//*").all()

                        for new_element in new_elements:
                            try:
                                element_data = await self._extract_element_data(new_element)
                                if element_data:
                                    signature = self._generate_element_signature(element_data)
                                    if signature not in unique_signatures:
                                        unique_signatures.add(signature)
                                        element_data["section"] = section.get("name", "Unknown")
                                        element_data["app_package"] = app_package
                                        element_data["collection_method"] = "dynamic_interaction"
                                        element_data["interaction_trigger"] = f"click_element_{i+1}"
                                        all_elements.append(element_data)
                            except Exception as e:
                                logger.debug(f"Failed to process dynamic element: {e}")
                                continue

                        # Navigate back if possible
                        try:
                            device.press("back")
                            await asyncio.sleep(1)
                        except Exception as back_e:
                            logger.debug(f"Failed to navigate back: {back_e}")

                except Exception as e:
                    logger.debug(f"Dynamic interaction {i+1} failed: {e}")
                    continue

            console.print(f"[green]✅ Dynamic content collection complete: {len(all_elements)} elements collected[/green]")

            return {
                "success": len(all_elements) > 0,
                "elements": all_elements,
                "interactions_performed": interactions_performed,
                "unique_elements": len(unique_signatures)
            }

        except Exception as e:
            logger.error(f"Dynamic content collection failed: {e}")
            return {"success": False, "elements": [], "error": str(e)}

    async def _create_checkpoint(self, device, section: Dict, section_index: int) -> Dict[str, Any]:
        """
        Create a checkpoint before processing a section
        """
        try:
            checkpoint = {
                "timestamp": datetime.now().isoformat(),
                "section": section["name"],
                "section_index": section_index,
                "app_state": await self._capture_app_state(device),
                "elements_count_before": self.collection_state["elements_found"]
            }

            self.checkpoints.append(checkpoint)
            self.collection_state["last_checkpoint"] = checkpoint

            console.print(f"[cyan]📍 Checkpoint created for section: {section['name']}[/cyan]")
            return checkpoint

        except Exception as e:
            logger.warning(f"Checkpoint creation failed: {e}")
            return {}

    async def _capture_app_state(self, device) -> Dict[str, Any]:
        """
        Capture current app state for recovery purposes
        """
        try:
            state = {
                "current_app": device.app_current().get("package", "unknown"),
                "screen_elements_count": len(device.xpath("//*").all()),
                "timestamp": datetime.now().isoformat()
            }
            return state

        except Exception as e:
            logger.debug(f"App state capture failed: {e}")
            return {}

    async def _attempt_section_recovery(self, device, section: Dict, checkpoint: Dict) -> Dict[str, Any]:
        """
        Attempt to recover from section failure using checkpoint
        """
        try:
            console.print(f"[yellow]🔄 Attempting recovery for section: {section['name']}[/yellow]")

            self.collection_state["recovery_count"] += 1

            # Try to restore app state
            recovery_success = await self._restore_from_checkpoint(device, checkpoint)

            if recovery_success:
                # Retry collection with emergency strategy
                recovery_result = await self._strategy_emergency_collection(device, section, checkpoint.get("app_state", {}).get("current_app", "unknown"))

                console.print(f"[green]✅ Recovery successful: {recovery_result.get('elements_collected', 0)} elements[/green]")
                return recovery_result
            else:
                console.print(f"[red]❌ Recovery failed for section: {section['name']}[/red]")
                return {"success": False, "elements_collected": 0}

        except Exception as e:
            logger.error(f"Section recovery failed: {e}")
            return {"success": False, "elements_collected": 0}

    async def _restore_from_checkpoint(self, device, checkpoint: Dict) -> bool:
        """
        Restore app state from checkpoint
        """
        try:
            # Basic recovery: ensure app is running
            target_app = checkpoint.get("app_state", {}).get("current_app", "com.kemendikdasmen.rumahpendidikan")

            current_app = device.app_current().get("package", "")
            if current_app != target_app:
                device.app_start(target_app)
                await asyncio.sleep(3)

            return True

        except Exception as e:
            logger.warning(f"Checkpoint restoration failed: {e}")
            return False

    async def _save_progress_checkpoint(self, total_elements: int, sections_completed: int):
        """
        Save progress checkpoint
        """
        try:
            self.collection_state["elements_found"] = total_elements
            self.collection_state["sections_completed_count"] = sections_completed
            self.collection_state["last_update"] = datetime.now().isoformat()

            # Save to persistent storage if available
            if self.persistent_excel_manager:
                self.persistent_excel_manager.save_to_persistent_file()
                console.print(f"[cyan]💾 Progress saved: {total_elements} elements, {sections_completed} sections[/cyan]")

        except Exception as e:
            logger.warning(f"Progress checkpoint save failed: {e}")

    async def _collect_navigation_elements_robustly(self, device, app_package: str) -> Dict[str, Any]:
        """
        Collect navigation elements robustly
        """
        try:
            console.print("[cyan]🧭 Collecting navigation elements...[/cyan]")

            navigation_section = {
                "name": "Navigation",
                "type": "navigation",
                "elements": self.menu_structure["navigation_elements"]
            }

            nav_result = await self._collect_section_elements_robustly(device, navigation_section, app_package)

            console.print(f"[green]✅ Navigation collection: {nav_result.get('elements_collected', 0)} elements[/green]")
            return nav_result

        except Exception as e:
            logger.error(f"Navigation collection failed: {e}")
            return {"success": False, "elements_collected": 0}

    async def _emergency_collection_fallback(self, device, app_package: str) -> Dict[str, Any]:
        """
        Emergency fallback collection when all else fails
        """
        try:
            console.print("[red]🚨 EMERGENCY FALLBACK COLLECTION[/red]")

            # Use the most basic collection possible
            emergency_section = {"name": "Emergency", "type": "emergency"}
            emergency_result = await self._strategy_emergency_collection(device, emergency_section, app_package)

            return {
                "success": True,
                "total_elements": emergency_result.get("elements_collected", 0),
                "collection_strategy": "emergency_fallback",
                "warning": "Emergency collection used - may be incomplete"
            }

        except Exception as e:
            logger.error(f"Emergency fallback failed: {e}")
            return {
                "success": False,
                "total_elements": 0,
                "collection_strategy": "failed",
                "error": str(e)
            }

    async def _verify_collection_completeness(self, collection_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Verify collection completeness and quality
        """
        try:
            console.print("[cyan]🔍 Verifying collection completeness...[/cyan]")

            total_elements = collection_result.get("total_elements", 0)
            sections_completed = collection_result.get("sections_completed", 0)
            total_sections = len(self.menu_structure["main_sections"])

            completion_percentage = (sections_completed / total_sections * 100) if total_sections > 0 else 0

            quality_score = self._calculate_quality_score(collection_result)

            verification_result = {
                **collection_result,
                "completion_percentage": completion_percentage,
                "quality_score": quality_score,
                "verification_timestamp": datetime.now().isoformat(),
                "collection_summary": {
                    "total_elements": total_elements,
                    "sections_completed": sections_completed,
                    "total_sections": total_sections,
                    "recovery_attempts": self.collection_state.get("recovery_count", 0)
                }
            }

            console.print(f"[green]✅ Collection verification complete: {completion_percentage:.1f}% sections, Quality: {quality_score:.1f}/10[/green]")
            return verification_result

        except Exception as e:
            logger.error(f"Collection verification failed: {e}")
            return collection_result

    def _calculate_quality_score(self, collection_result: Dict[str, Any]) -> float:
        """
        Calculate quality score for the collection
        """
        try:
            score = 0.0

            # Elements count score (0-4 points)
            total_elements = collection_result.get("total_elements", 0)
            if total_elements >= 200:
                score += 4.0
            elif total_elements >= 100:
                score += 3.0
            elif total_elements >= 50:
                score += 2.0
            elif total_elements >= 10:
                score += 1.0

            # Sections completion score (0-3 points)
            sections_completed = collection_result.get("sections_completed", 0)
            total_sections = len(self.menu_structure["main_sections"])
            completion_ratio = sections_completed / total_sections if total_sections > 0 else 0
            score += completion_ratio * 3.0

            # Strategy success score (0-2 points)
            strategy = collection_result.get("collection_strategy", "")
            if strategy == "progressive_with_checkpoints":
                score += 2.0
            elif strategy == "emergency_fallback":
                score += 1.0

            # Recovery penalty (0-1 points deduction)
            recovery_count = collection_result.get("recovery_count", 0)
            if recovery_count == 0:
                score += 1.0
            elif recovery_count <= 2:
                score += 0.5

            return min(score, 10.0)  # Cap at 10.0

        except Exception as e:
            logger.debug(f"Quality score calculation failed: {e}")
            return 5.0  # Default middle score
